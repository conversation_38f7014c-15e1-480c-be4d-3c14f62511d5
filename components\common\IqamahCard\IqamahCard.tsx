import React, { useState, useEffect } from "react";
import "./style.css";
import NumberInput from "../NumberInput/NumberInput";
import { FormDataStorage } from "../../../pages/register/FormPage";

interface IqamahCardProps {
  prayer: string;
  whenTime?: string;
}

const IqamahCard: React.FC<IqamahCardProps> = ({
  prayer,
  whenTime = "pas ezanit",
}) => {
  const formData: FormDataStorage | null = JSON.parse(
    localStorage.getItem("formData") || "null"
  );

  const [showOnTV, setShowOnTV] = useState<boolean>(
    formData?.prayers[prayer]?.showOnTV || false
  );
  const [minutes, setMinutes] = useState<number>(
    formData?.prayers[prayer]?.timeAfterAdhan || 1
  );

  const handleCheckboxChange = () => {
    setShowOnTV((prev) => !prev);
  };

  const incrementMinutes = () => {
    setMinutes((prev) => prev + 1);
  };

  const decrementMinutes = () => {
    setMinutes((prev) => (prev > 1 ? prev - 1 : prev)); // Enforce a minimum of 2
  };

  // Update localStorage whenever state changes
  useEffect(() => {
    const currentFormData: FormDataStorage = JSON.parse(
      localStorage.getItem("formData") || "{}"
    );

    const updatedFormData: FormDataStorage = {
      ...currentFormData,
      prayers: {
        ...currentFormData.prayers,
        [prayer]: {
          timeAfterAdhan: whenTime === "pas ezanit" ? minutes : 0,
          timeBeforeSunRise: whenTime !== "pas ezanit" ? minutes : 0,
          showOnTV,
        },
      },
    };

    localStorage.setItem("formData", JSON.stringify(updatedFormData));
  }, [minutes, prayer, showOnTV, whenTime]); // Only update when `minutes` or `showOnTV` changes

  return (
    <div className="iqamah-card">
      <p className="iqamah-time">
        <span style={{ fontWeight: "600" }}>{prayer.toUpperCase()}</span> - 
        Ikameti <span className="placeholder">{minutes} minuta</span> {whenTime}
      </p>
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "space-between",
        }}
      >
        <NumberInput
          minutes={minutes}
          incrementMinutes={incrementMinutes}
          decrementMinutes={decrementMinutes}
        />
        <div className="checkbox-container">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={showOnTV}
              color="darkcyan"
              className="checkbox-show-in-tv"
              onChange={handleCheckboxChange}
              style={{ marginRight: 5, color: "darkcyan" }}
            />
            Shfaq në TV
          </label>
        </div>
      </div>
    </div>
  );
};

export default IqamahCard;
