{"ast": null, "code": "'use strict';\n\nfunction isValidDate(date) {\n  // Taken from http://stackoverflow.com/a/12372720/1562178\n  // If getTime() returns NaN it'll return false anyway\n  return date.getTime() === date.getTime();\n}\nmodule.exports = {\n  isValidDate\n};", "map": {"version": 3, "names": ["isValidDate", "date", "getTime", "module", "exports"], "sources": ["C:/Users/<USER>/Documents/ekrani/node_modules/node-schedule/lib/utils/dateUtils.js"], "sourcesContent": ["'use strict';\n\nfunction isValidDate(date) {\n  // Taken from http://stackoverflow.com/a/12372720/1562178\n  // If getTime() returns NaN it'll return false anyway\n  return date.getTime() === date.getTime();\n}\n\nmodule.exports = {\n  isValidDate\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,WAAWA,CAACC,IAAI,EAAE;EACzB;EACA;EACA,OAAOA,IAAI,CAACC,OAAO,CAAC,CAAC,KAAKD,IAAI,CAACC,OAAO,CAAC,CAAC;AAC1C;AAEAC,MAAM,CAACC,OAAO,GAAG;EACfJ;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}