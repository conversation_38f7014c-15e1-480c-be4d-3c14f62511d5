import { useEffect, useState } from "react";
import hadiths from "../../data/hadiths.json";
import "./hadith-holder-style.css";
import { FormDataStorage } from "../../pages/register/FormPage";
import { useNavigate } from "react-router-dom";
import HadithHeader from "../header/hadith-header";
import { setupMidnightInterval } from "../../utils/mid-night";
import { getNextPrayerTime } from "../../utils/prayer-times.service";

const HadithHolder = () => {
  const [currentHadith, setCurrentHadith] = useState<any>();
  const [currentAttention, setCurrentAttention] = useState({
    title: "",
    description: "",
  });
  const [nextPrayer, setNextPrayer] = useState<any>(null);
  const [showTextContent, setShowTextContent] = useState(true);
  const [showNextPrayer, setShowNextPrayer] = useState(false);

  useEffect(() => {
    getNextPrayerTime().then((res) => {
      setNextPrayer(res);
    });

    const interval = setInterval(() => {
      getNextPrayerTime().then((res) => {
        setNextPrayer(res);
      });
    }, 60000);
    
    return () => {
      clearInterval(interval);
    };
  }, []);

  const getCurrentHadith = () => {
    const formData: FormDataStorage = JSON.parse(
      localStorage.getItem("formData") || "null"
    );

    if (
      formData?.attentionText?.title ||
      formData?.attentionText?.description
    ) {
      setCurrentAttention({
        title: formData?.attentionText?.title || "",
        description: formData?.attentionText?.description || "",
      });
    } else {
      const randomIndex = Math.floor(Math.random() * hadiths.data.length);
      const randomHadith = hadiths.data[randomIndex];
      setCurrentHadith(randomHadith);
    }
  };

  // Define intervalCallback function here
  function intervalCallback() {
    getCurrentHadith(); // Ensure to update the hadith every midnight
  }

  useEffect(() => {
    getCurrentHadith();
    const cleanup = setupMidnightInterval(intervalCallback, intervalCallback);

    return () => {
      cleanup();
    };
  }, []);

  useEffect(() => {
    const displayCycle = () => {
      // Show textContent for 10 seconds
      setShowTextContent(true);
      setShowNextPrayer(false);

      setTimeout(() => {
        // Show nextPrayer for 5 seconds
        setShowTextContent(false);
        setShowNextPrayer(true);

        setTimeout(() => {
          // Reset to show textContent again
          setShowNextPrayer(false);
        }, 15000); // 15 seconds for nextPrayer
      }, 40000); // 40 seconds for textContent
    };

    displayCycle(); // Start the initial cycle
    const interval = setInterval(displayCycle, 55000); // Repeat the cycle every 15 seconds

    return () => {
      clearInterval(interval);
    };
  }, []);

  const navigate = useNavigate();
  const haveData = JSON.parse(localStorage.getItem("formData") || "null");

  const returnPaddingTopPercentage = () => {
    if (currentHadith?.textContent.length < 200) {
      return "9%";
    } else if (currentHadith?.textContent.length < 220) {
      return "7%";
    } else if (currentHadith?.textContent.length < 250) {
      return "6%";
    } else if (currentHadith?.textContent.length < 270) {
      return "5%";
    } else if (currentHadith?.textContent.length < 290) {
      return "4%";
    } else {
      return "2%";
    }
  };

  if (!haveData) {
    setTimeout(() => {
      navigate("/form");
    }, 0);
    return <></>;
  }

  return (
    <div className="hadith-container">
      <div className="hadith-info">
        <HadithHeader />
        {currentAttention?.description || currentAttention?.title ? (
          <div className="hadith-innerHtml" style={{ paddingTop: "7%" }}>
            <p className="info-title" style={{ fontSize: 38 }}>
              {currentAttention?.title}
            </p>
            <p className="info-description">{currentAttention?.description}</p>
          </div>
        ) : (
          <div
            className={`hadith-innerHtml `}
            style={{
              paddingTop: returnPaddingTopPercentage(),
            }}
          >
            {showTextContent && (
              <div>
                {currentHadith?.entryText && (
                  <span
                    style={{ fontSize: 24, color: "rgba(255,255,255,0.8)" }}
                  >
                    {currentHadith?.entryText}
                  </span>
                )}
                <p
                  className="info-title"
                  style={{
                    fontWeight: currentHadith?.entryText.length > 1 ? 500 : 400,
                    fontSize:
                      currentHadith?.textContent.length > 270 ||
                      currentHadith?.entryText.length > 30
                        ? 35
                        : 37,
                  }}
                >
                  {currentHadith?.textContent}
                </p>
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    justifyContent: "flex-start",
                  }}
                >
                  <p style={{ fontSize: 24 }} className="info-description">
                    ({currentHadith?.reference})
                  </p>
                </div>
              </div>
            )}

            {showNextPrayer && (
              <p
                className="info-title"
                style={{ fontSize: 55, textAlign: "center", marginTop: "5%" }}
              >
                {nextPrayer}
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default HadithHolder;
