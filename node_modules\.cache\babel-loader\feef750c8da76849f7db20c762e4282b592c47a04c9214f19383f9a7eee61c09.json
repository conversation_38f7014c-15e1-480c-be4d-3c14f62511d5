{"ast": null, "code": "import schedule from 'node-schedule';\nexport const schedulePageRefresh = () => {\n  schedule.scheduleJob('0 1,3,5,7 * * *', () => {\n    console.log('Refreshing the page at 1, 3, 5, or 7 AM');\n    window.location.reload();\n  });\n};", "map": {"version": 3, "names": ["schedule", "schedulePageRefresh", "scheduleJob", "console", "log", "window", "location", "reload"], "sources": ["C:/Users/<USER>/Documents/ekrani/src/utils/scheduler.ts"], "sourcesContent": ["import schedule from 'node-schedule';\n\nexport const schedulePageRefresh = (): void => {\n    schedule.scheduleJob('0 1,3,5,7 * * *', () => {\n      console.log('Refreshing the page at 1, 3, 5, or 7 AM');\n      window.location.reload();\n    });\n  };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AAEpC,OAAO,MAAMC,mBAAmB,GAAGA,CAAA,KAAY;EAC3CD,QAAQ,CAACE,WAAW,CAAC,iBAAiB,EAAE,MAAM;IAC5CC,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IACtDC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}