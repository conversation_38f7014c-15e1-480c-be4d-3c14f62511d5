{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ekrani\\\\src\\\\App.tsx\";\nimport { BrowserRouter as Router, Route, Routes } from \"react-router-dom\";\nimport FormPage from \"../pages/register/FormPage\";\nimport SettingsPage from \"../pages/settings/SettingsPage\";\nimport { HomePage } from \"../pages/home/<USER>\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/form\",\n        Component: FormPage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        Component: HomePage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/settings\",\n        Component: SettingsPage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Route", "Routes", "FormPage", "SettingsPage", "HomePage", "jsxDEV", "_jsxDEV", "App", "children", "path", "Component", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/ekrani/src/App.tsx"], "sourcesContent": ["import { BrowserRouter as Router, Route, Routes } from \"react-router-dom\";\r\nimport FormPage from \"../pages/register/FormPage\";\r\nimport SettingsPage from \"../pages/settings/SettingsPage\";\r\nimport { HomePage } from \"../pages/home/<USER>\";\r\n\r\nfunction App() {\r\n  return (\r\n    <Router>\r\n      <Routes>\r\n        <Route path=\"/form\" Component={FormPage} />\r\n        <Route path=\"/\" Component={HomePage} />\r\n        <Route path=\"/settings\" Component={SettingsPage} />\r\n      </Routes>\r\n    </Router>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";AAAA,SAASA,aAAa,IAAIC,MAAM,EAAEC,KAAK,EAAEC,MAAM,QAAQ,kBAAkB;AACzE,OAAOC,QAAQ,MAAM,4BAA4B;AACjD,OAAOC,YAAY,MAAM,gCAAgC;AACzD,SAASC,QAAQ,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACP,MAAM;IAAAS,QAAA,eACLF,OAAA,CAACL,MAAM;MAAAO,QAAA,gBACLF,OAAA,CAACN,KAAK;QAACS,IAAI,EAAC,OAAO;QAACC,SAAS,EAAER;MAAS;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3CR,OAAA,CAACN,KAAK;QAACS,IAAI,EAAC,GAAG;QAACC,SAAS,EAAEN;MAAS;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvCR,OAAA,CAACN,KAAK;QAACS,IAAI,EAAC,WAAW;QAACC,SAAS,EAAEP;MAAa;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb;AAACC,EAAA,GAVQR,GAAG;AAYZ,eAAeA,GAAG;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}