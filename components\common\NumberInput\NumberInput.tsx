import React from "react";
import "./style.css";

interface NumberInputProps {
  minutes: number; // Current minutes value to display
  incrementMinutes: () => void; // Function to handle incrementing minutes
  decrementMinutes: () => void; // Function to handle decrementing minutes
}

const NumberInput: React.FC<NumberInputProps> = ({
  minutes,
  incrementMinutes,
  decrementMinutes,
}) => {
  
  return (
    <div className="number-input-container">
      <button
        type="button"
        onClick={decrementMinutes}
        className="number-input-button"
      >
        -
      </button>
      <input
        type="number"
        value={minutes}
        readOnly
        className="number-input-field"
      />
      <button
        type="button"
        onClick={incrementMinutes}
        className="number-input-button"
      >
        +
      </button>
    </div>
  );
};

export default NumberInput;
