import PrayerTime<PERSON>osovo from "../data/KS.json";
import moment, { Moment } from "moment";
import { Prayer, PrayerNames } from "./prayer.model";
import { PrayerLable } from "./prayer-lable.enum";

// let todayPrayer: Prayer;

/**
 * Retrieves the prayer times for the current day and the next day's imsaku time.
 * This function assumes the existence of a 'PrayerTimeKosovo' array containing the prayer time data.
 * It returns an object with the prayer times for the current day and the next day's imsaku time.
 * If the current day is the last day of the month, it wraps around to the next month for the next day's prayer times.
 */
export const getTodayPrayerTimes = () => {
  const today = new Date();
  const currentMonth = today.getMonth() + 1;
  const currentDate = today.getDate();

  const todayPrayerTimes = PrayerTimeKosovo.find(
    (time) => time.Muaji === currentMonth && time.Data === currentDate
  ) as any;

  let nextMonth = currentMonth;
  let nextDate = currentDate + 1;

  if (nextDate > lastDayOfMonth(today.getFullYear(), currentMonth - 1)) {
    nextMonth++;
    nextDate = 1;
  }

  if (nextMonth > 12) {
    nextMonth = 1;
  }

  const nextPrayer = PrayerTimeKosovo.find(
    (time) => time.Muaji === nextMonth && time.Data === nextDate
  );
  const nextImsaku = nextPrayer?.Imsaku;

  const todayPrayer = {
    nextImsaku: nextImsaku as string,
    todayPrayerTimes: todayPrayerTimes as PrayerNames,
  };

  localStorage.setItem("todayPrayer", JSON.stringify(todayPrayer));

  return { todayPrayerTimes, nextImsaku };
};

function lastDayOfMonth(year: number, month: number): number {
  return new Date(year, month + 1, 0).getDate();
}
/**
 * Retrieves the next prayer time based on the current time and available prayer times.
 * This function assumes the existence of a 'getTodayPrayerTimes' function and a 'PrayerLable' array.
 * It returns a formatted string indicating the time remaining until the next prayer.
 * If all prayers are finished, it assumes the next day's imsaku prayer time.
 */
export const getActivePrayer = () => {
  const prayerTime: Prayer = JSON.parse(
    localStorage.getItem("todayPrayer") || "{}"
  );
  const { todayPrayerTimes } = prayerTime;

  if (!prayerTime || !prayerTime.todayPrayerTimes) {
    // Handle case when todayPrayerTimes is undefined
    console.error("todayPrayerTimes is missing or undefined");
    return null; // Return early if todayPrayerTimes is undefined
  }

  if (todayPrayerTimes) {
    delete todayPrayerTimes["Data" as any];
    delete todayPrayerTimes["Muaji" as any];
  }

  const now: Moment = moment().startOf("minute"); // Current time rounded down to the minute
  // Iterate over the prayer times

  const sunriseTime: Moment | null = todayPrayerTimes["L. e Diellit"]
    ? moment(todayPrayerTimes["L. e Diellit"], "HH:mm")
    : null;
  const dhuhrTime: Moment | null = todayPrayerTimes["Dreka"]
    ? moment(todayPrayerTimes["Dreka"], "HH:mm")
    : null;

  // If the current time is between Sunrise and Dhuhr, return null (no active prayer)
  if (now.isAfter(sunriseTime) && now.isBefore(dhuhrTime)) {
    return null; // No prayer should be active
  }

  let activePrayer: [string, string] | undefined;

  if (todayPrayerTimes) {
    Object.entries(todayPrayerTimes).forEach(([prayerName, prayerTime]) => {
      const prayerMoment: Moment = moment(prayerTime, "HH:mm");
      if (now.isSameOrAfter(prayerMoment)) {
        activePrayer = [prayerName, prayerTime];
      }
    });
  }

  if (activePrayer) {
    const [prayerName] = activePrayer;
    return prayerName;
  } else {
    return "Imsaku";
  }
};

export const getNextPrayerTime = async () => {
  const todayPrayers: Prayer = JSON.parse(
    localStorage.getItem("todayPrayer") || "{}"
  );
  const { todayPrayerTimes, nextImsaku } = todayPrayers;
  
  // Remove unnecessary labels from times
  Object.keys(todayPrayerTimes || {})
    .filter((x) => !PrayerLable.includes(x))
    .forEach((item) => {
      delete todayPrayerTimes[item as any];
    });

  const now: Moment = moment().startOf("minute"); // Current time rounded down to the minute
  const nextPrayer: Moment | undefined = Object.values(todayPrayerTimes)
    .map((time) => moment(time, "HH:mm"))
    .sort((a, b) => a.diff(b))
    .find((time) => time.isAfter(now));

  let nextTime = nextImsaku; // Assume that all prayers are finished, so get the next day's imsaku
  let nextTimeLabel: string = "Imsaku"; // Default label is "Imsaku"
  let minutes: number;

  if (nextPrayer) {
    nextTime = nextPrayer.format("HH:mm");
    nextTimeLabel =
      Object.keys(todayPrayerTimes).find(
        (key) => todayPrayerTimes[key as any] === nextTime
      ) || "Imsaku";
    minutes = nextPrayer.diff(now, "minutes");
  } else {
    const time: Moment = moment(nextTime, "HH:mm");
    minutes = time.add(1, "day").diff(now, "minutes");
  }
  const remainedHours: number = Math.floor(minutes / 60);
  const remainedMinutes: number = minutes % 60;
  let timeInWords: string = nextTimeLabel;
  if (remainedHours !== 0 || remainedMinutes !== 0) {
    timeInWords += " edhe ";
    if (remainedHours > 0) {
      timeInWords += `${remainedHours} orë ${remainedMinutes > 0 ? "e " : ""}`;
    }
    if (remainedMinutes > 0) {
      timeInWords += `${remainedMinutes} minut${
        remainedMinutes > 1 ? "a" : "ë"
      }`;
    }
  }
  return timeInWords;
};
