import "./prayer-times-holder-style.css";

const PrayerTime = ({
  title,
  time,
  icon,
  active,
  showIqamah,
  iqamahTime,
}: any) => {
  console.log("time: ", time);
  console.log("iqamahTime: ", iqamahTime);

  // check if it's Winter or Summer time, depending if d<PERSON><PERSON> is earlier than 12 : 00 
  function isTimeAfterNoon(time: string): boolean {
    const [hours, minutes] = time.split(":").map(Number);
    const totalMinutes = hours * 60 + minutes;
    // 12:00 in total minutes
    const noonMinutes = 12 * 60;
    // Compare total minutes
    return totalMinutes > noonMinutes;
  }

  function calculateIqamahTime(time: string, iqamahTime: number): string {
    // Split the time into hours and minutes

    if (title === "Dreka") {
      if (time && isTimeAfterNoon(time)) time = "13:00";
      else time = "12:00";
    }
    if (showIqamah && time) {
      const [hours, minutes] = time?.split(":")?.map(Number);
      // Convert the time into total minutes
      const totalMinutes = hours * 60 + minutes + iqamahTime;
      // Calculate new hours and minutes
      const newHours = Math.floor(totalMinutes / 60) % 24; // Handle 24-hour format
      const newMinutes = totalMinutes % 60;
      // Return the formatted result
      return `${newHours.toString().padStart(2, "0")}:${newMinutes
        .toString()
        .padStart(2, "0")}`;
    }
    return "";
  }

  return (
    <div className={`prayer-time-conatiner ${active && "shadow"}`}>
      <img
        src={icon}
        className={`prayer-icon ${active && "active-icon"}`}
        alt=""
      />

      <p
        className="prayer-title"
        style={{ fontWeight: active ? "bold" : "normal" }}
      >
        {title}
      </p>

      <p
        className="prayer-time"
        style={{ fontWeight: active ? "bold" : "normal" }}
      >
        {time}
      </p>
      {showIqamah && (
        <p
          className="prayer-iqamah"
          style={{
            fontWeight: active ? "bold" : "normal",
            color: "whitesmoke",
            marginTop: 10,
          }}
        >
          Ikameti: {calculateIqamahTime(time, iqamahTime)}
        </p>
      )}
    </div>
  );
};

export default PrayerTime;
