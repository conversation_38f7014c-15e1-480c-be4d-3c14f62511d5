{"ast": null, "code": "'use strict';\n\n// Load Date class extensions\nvar CronDate = require('./date');\nvar stringifyField = require('./field_stringify');\n\n/**\n * Cron iteration loop safety limit\n */\nvar LOOP_LIMIT = 10000;\n\n/**\n * Construct a new expression parser\n *\n * Options:\n *   currentDate: iterator start date\n *   endDate: iterator end date\n *\n * @constructor\n * @private\n * @param {Object} fields  Expression fields parsed values\n * @param {Object} options Parser options\n */\nfunction CronExpression(fields, options) {\n  this._options = options;\n  this._utc = options.utc || false;\n  this._tz = this._utc ? 'UTC' : options.tz;\n  this._currentDate = new CronDate(options.currentDate, this._tz);\n  this._startDate = options.startDate ? new CronDate(options.startDate, this._tz) : null;\n  this._endDate = options.endDate ? new CronDate(options.endDate, this._tz) : null;\n  this._isIterator = options.iterator || false;\n  this._hasIterated = false;\n  this._nthDayOfWeek = options.nthDayOfWeek || 0;\n  this.fields = CronExpression._freezeFields(fields);\n}\n\n/**\n * Field mappings\n * @type {Array}\n */\nCronExpression.map = ['second', 'minute', 'hour', 'dayOfMonth', 'month', 'dayOfWeek'];\n\n/**\n * Prefined intervals\n * @type {Object}\n */\nCronExpression.predefined = {\n  '@yearly': '0 0 1 1 *',\n  '@monthly': '0 0 1 * *',\n  '@weekly': '0 0 * * 0',\n  '@daily': '0 0 * * *',\n  '@hourly': '0 * * * *'\n};\n\n/**\n * Fields constraints\n * @type {Array}\n */\nCronExpression.constraints = [{\n  min: 0,\n  max: 59,\n  chars: []\n},\n// Second\n{\n  min: 0,\n  max: 59,\n  chars: []\n},\n// Minute\n{\n  min: 0,\n  max: 23,\n  chars: []\n},\n// Hour\n{\n  min: 1,\n  max: 31,\n  chars: ['L']\n},\n// Day of month\n{\n  min: 1,\n  max: 12,\n  chars: []\n},\n// Month\n{\n  min: 0,\n  max: 7,\n  chars: ['L']\n} // Day of week\n];\n\n/**\n * Days in month\n * @type {number[]}\n */\nCronExpression.daysInMonth = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\n\n/**\n * Field aliases\n * @type {Object}\n */\nCronExpression.aliases = {\n  month: {\n    jan: 1,\n    feb: 2,\n    mar: 3,\n    apr: 4,\n    may: 5,\n    jun: 6,\n    jul: 7,\n    aug: 8,\n    sep: 9,\n    oct: 10,\n    nov: 11,\n    dec: 12\n  },\n  dayOfWeek: {\n    sun: 0,\n    mon: 1,\n    tue: 2,\n    wed: 3,\n    thu: 4,\n    fri: 5,\n    sat: 6\n  }\n};\n\n/**\n * Field defaults\n * @type {Array}\n */\nCronExpression.parseDefaults = ['0', '*', '*', '*', '*', '*'];\nCronExpression.standardValidCharacters = /^[,*\\d/-]+$/;\nCronExpression.dayOfWeekValidCharacters = /^[?,*\\dL#/-]+$/;\nCronExpression.dayOfMonthValidCharacters = /^[?,*\\dL/-]+$/;\nCronExpression.validCharacters = {\n  second: CronExpression.standardValidCharacters,\n  minute: CronExpression.standardValidCharacters,\n  hour: CronExpression.standardValidCharacters,\n  dayOfMonth: CronExpression.dayOfMonthValidCharacters,\n  month: CronExpression.standardValidCharacters,\n  dayOfWeek: CronExpression.dayOfWeekValidCharacters\n};\nCronExpression._isValidConstraintChar = function _isValidConstraintChar(constraints, value) {\n  if (typeof value !== 'string') {\n    return false;\n  }\n  return constraints.chars.some(function (char) {\n    return value.indexOf(char) > -1;\n  });\n};\n\n/**\n * Parse input interval\n *\n * @param {String} field Field symbolic name\n * @param {String} value Field value\n * @param {Array} constraints Range upper and lower constraints\n * @return {Array} Sequence of sorted values\n * @private\n */\nCronExpression._parseField = function _parseField(field, value, constraints) {\n  // Replace aliases\n  switch (field) {\n    case 'month':\n    case 'dayOfWeek':\n      var aliases = CronExpression.aliases[field];\n      value = value.replace(/[a-z]{3}/gi, function (match) {\n        match = match.toLowerCase();\n        if (typeof aliases[match] !== 'undefined') {\n          return aliases[match];\n        } else {\n          throw new Error('Validation error, cannot resolve alias \"' + match + '\"');\n        }\n      });\n      break;\n  }\n\n  // Check for valid characters.\n  if (!CronExpression.validCharacters[field].test(value)) {\n    throw new Error('Invalid characters, got value: ' + value);\n  }\n\n  // Replace '*' and '?'\n  if (value.indexOf('*') !== -1) {\n    value = value.replace(/\\*/g, constraints.min + '-' + constraints.max);\n  } else if (value.indexOf('?') !== -1) {\n    value = value.replace(/\\?/g, constraints.min + '-' + constraints.max);\n  }\n\n  //\n  // Inline parsing functions\n  //\n  // Parser path:\n  //  - parseSequence\n  //    - parseRepeat\n  //      - parseRange\n\n  /**\n   * Parse sequence\n   *\n   * @param {String} val\n   * @return {Array}\n   * @private\n   */\n  function parseSequence(val) {\n    var stack = [];\n    function handleResult(result) {\n      if (result instanceof Array) {\n        // Make sequence linear\n        for (var i = 0, c = result.length; i < c; i++) {\n          var value = result[i];\n          if (CronExpression._isValidConstraintChar(constraints, value)) {\n            stack.push(value);\n            continue;\n          }\n          // Check constraints\n          if (typeof value !== 'number' || Number.isNaN(value) || value < constraints.min || value > constraints.max) {\n            throw new Error('Constraint error, got value ' + value + ' expected range ' + constraints.min + '-' + constraints.max);\n          }\n          stack.push(value);\n        }\n      } else {\n        // Scalar value\n\n        if (CronExpression._isValidConstraintChar(constraints, result)) {\n          stack.push(result);\n          return;\n        }\n        var numResult = +result;\n\n        // Check constraints\n        if (Number.isNaN(numResult) || numResult < constraints.min || numResult > constraints.max) {\n          throw new Error('Constraint error, got value ' + result + ' expected range ' + constraints.min + '-' + constraints.max);\n        }\n        if (field === 'dayOfWeek') {\n          numResult = numResult % 7;\n        }\n        stack.push(numResult);\n      }\n    }\n    var atoms = val.split(',');\n    if (!atoms.every(function (atom) {\n      return atom.length > 0;\n    })) {\n      throw new Error('Invalid list value format');\n    }\n    if (atoms.length > 1) {\n      for (var i = 0, c = atoms.length; i < c; i++) {\n        handleResult(parseRepeat(atoms[i]));\n      }\n    } else {\n      handleResult(parseRepeat(val));\n    }\n    stack.sort(CronExpression._sortCompareFn);\n    return stack;\n  }\n\n  /**\n   * Parse repetition interval\n   *\n   * @param {String} val\n   * @return {Array}\n   */\n  function parseRepeat(val) {\n    var repeatInterval = 1;\n    var atoms = val.split('/');\n    if (atoms.length > 2) {\n      throw new Error('Invalid repeat: ' + val);\n    }\n    if (atoms.length > 1) {\n      if (atoms[0] == +atoms[0]) {\n        atoms = [atoms[0] + '-' + constraints.max, atoms[1]];\n      }\n      return parseRange(atoms[0], atoms[atoms.length - 1]);\n    }\n    return parseRange(val, repeatInterval);\n  }\n\n  /**\n   * Parse range\n   *\n   * @param {String} val\n   * @param {Number} repeatInterval Repetition interval\n   * @return {Array}\n   * @private\n   */\n  function parseRange(val, repeatInterval) {\n    var stack = [];\n    var atoms = val.split('-');\n    if (atoms.length > 1) {\n      // Invalid range, return value\n      if (atoms.length < 2) {\n        return +val;\n      }\n      if (!atoms[0].length) {\n        if (!atoms[1].length) {\n          throw new Error('Invalid range: ' + val);\n        }\n        return +val;\n      }\n\n      // Validate range\n      var min = +atoms[0];\n      var max = +atoms[1];\n      if (Number.isNaN(min) || Number.isNaN(max) || min < constraints.min || max > constraints.max) {\n        throw new Error('Constraint error, got range ' + min + '-' + max + ' expected range ' + constraints.min + '-' + constraints.max);\n      } else if (min > max) {\n        throw new Error('Invalid range: ' + val);\n      }\n\n      // Create range\n      var repeatIndex = +repeatInterval;\n      if (Number.isNaN(repeatIndex) || repeatIndex <= 0) {\n        throw new Error('Constraint error, cannot repeat at every ' + repeatIndex + ' time.');\n      }\n\n      // JS DOW is in range of 0-6 (SUN-SAT) but we also support 7 in the expression\n      // Handle case when range contains 7 instead of 0 and translate this value to 0\n      if (field === 'dayOfWeek' && max % 7 === 0) {\n        stack.push(0);\n      }\n      for (var index = min, count = max; index <= count; index++) {\n        var exists = stack.indexOf(index) !== -1;\n        if (!exists && repeatIndex > 0 && repeatIndex % repeatInterval === 0) {\n          repeatIndex = 1;\n          stack.push(index);\n        } else {\n          repeatIndex++;\n        }\n      }\n      return stack;\n    }\n    return Number.isNaN(+val) ? val : +val;\n  }\n  return parseSequence(value);\n};\nCronExpression._sortCompareFn = function (a, b) {\n  var aIsNumber = typeof a === 'number';\n  var bIsNumber = typeof b === 'number';\n  if (aIsNumber && bIsNumber) {\n    return a - b;\n  }\n  if (!aIsNumber && bIsNumber) {\n    return 1;\n  }\n  if (aIsNumber && !bIsNumber) {\n    return -1;\n  }\n  return a.localeCompare(b);\n};\nCronExpression._handleMaxDaysInMonth = function (mappedFields) {\n  // Filter out any day of month value that is larger than given month expects\n  if (mappedFields.month.length === 1) {\n    var daysInMonth = CronExpression.daysInMonth[mappedFields.month[0] - 1];\n    if (mappedFields.dayOfMonth[0] > daysInMonth) {\n      throw new Error('Invalid explicit day of month definition');\n    }\n    return mappedFields.dayOfMonth.filter(function (dayOfMonth) {\n      return dayOfMonth === 'L' ? true : dayOfMonth <= daysInMonth;\n    }).sort(CronExpression._sortCompareFn);\n  }\n};\nCronExpression._freezeFields = function (fields) {\n  for (var i = 0, c = CronExpression.map.length; i < c; ++i) {\n    var field = CronExpression.map[i]; // Field name\n    var value = fields[field];\n    fields[field] = Object.freeze(value);\n  }\n  return Object.freeze(fields);\n};\nCronExpression.prototype._applyTimezoneShift = function (currentDate, dateMathVerb, method) {\n  if (method === 'Month' || method === 'Day') {\n    var prevTime = currentDate.getTime();\n    currentDate[dateMathVerb + method]();\n    var currTime = currentDate.getTime();\n    if (prevTime === currTime) {\n      // Jumped into a not existent date due to a DST transition\n      if (currentDate.getMinutes() === 0 && currentDate.getSeconds() === 0) {\n        currentDate.addHour();\n      } else if (currentDate.getMinutes() === 59 && currentDate.getSeconds() === 59) {\n        currentDate.subtractHour();\n      }\n    }\n  } else {\n    var previousHour = currentDate.getHours();\n    currentDate[dateMathVerb + method]();\n    var currentHour = currentDate.getHours();\n    var diff = currentHour - previousHour;\n    if (diff === 2) {\n      // Starting DST\n      if (this.fields.hour.length !== 24) {\n        // Hour is specified\n        this._dstStart = currentHour;\n      }\n    } else if (diff === 0 && currentDate.getMinutes() === 0 && currentDate.getSeconds() === 0) {\n      // Ending DST\n      if (this.fields.hour.length !== 24) {\n        // Hour is specified\n        this._dstEnd = currentHour;\n      }\n    }\n  }\n};\n\n/**\n * Find next or previous matching schedule date\n *\n * @return {CronDate}\n * @private\n */\nCronExpression.prototype._findSchedule = function _findSchedule(reverse) {\n  /**\n   * Match field value\n   *\n   * @param {String} value\n   * @param {Array} sequence\n   * @return {Boolean}\n   * @private\n   */\n  function matchSchedule(value, sequence) {\n    for (var i = 0, c = sequence.length; i < c; i++) {\n      if (sequence[i] >= value) {\n        return sequence[i] === value;\n      }\n    }\n    return sequence[0] === value;\n  }\n\n  /**\n   * Helps determine if the provided date is the correct nth occurence of the\n   * desired day of week.\n   *\n   * @param {CronDate} date\n   * @param {Number} nthDayOfWeek\n   * @return {Boolean}\n   * @private\n   */\n  function isNthDayMatch(date, nthDayOfWeek) {\n    if (nthDayOfWeek < 6) {\n      if (date.getDate() < 8 && nthDayOfWeek === 1 // First occurence has to happen in first 7 days of the month\n      ) {\n        return true;\n      }\n      var offset = date.getDate() % 7 ? 1 : 0; // Math is off by 1 when dayOfWeek isn't divisible by 7\n      var adjustedDate = date.getDate() - date.getDate() % 7; // find the first occurance\n      var occurrence = Math.floor(adjustedDate / 7) + offset;\n      return occurrence === nthDayOfWeek;\n    }\n    return false;\n  }\n\n  /**\n   * Helper function that checks if 'L' is in the array\n   *\n   * @param {Array} expressions\n   */\n  function isLInExpressions(expressions) {\n    return expressions.length > 0 && expressions.some(function (expression) {\n      return typeof expression === 'string' && expression.indexOf('L') >= 0;\n    });\n  }\n\n  // Whether to use backwards directionality when searching\n  reverse = reverse || false;\n  var dateMathVerb = reverse ? 'subtract' : 'add';\n  var currentDate = new CronDate(this._currentDate, this._tz);\n  var startDate = this._startDate;\n  var endDate = this._endDate;\n\n  // Find matching schedule\n  var startTimestamp = currentDate.getTime();\n  var stepCount = 0;\n  function isLastWeekdayOfMonthMatch(expressions) {\n    return expressions.some(function (expression) {\n      // There might be multiple expressions and not all of them will contain\n      // the \"L\".\n      if (!isLInExpressions([expression])) {\n        return false;\n      }\n\n      // The first character represents the weekday\n      var weekday = Number.parseInt(expression[0]) % 7;\n      if (Number.isNaN(weekday)) {\n        throw new Error('Invalid last weekday of the month expression: ' + expression);\n      }\n      return currentDate.getDay() === weekday && currentDate.isLastWeekdayOfMonth();\n    });\n  }\n  while (stepCount < LOOP_LIMIT) {\n    stepCount++;\n\n    // Validate timespan\n    if (reverse) {\n      if (startDate && currentDate.getTime() - startDate.getTime() < 0) {\n        throw new Error('Out of the timespan range');\n      }\n    } else {\n      if (endDate && endDate.getTime() - currentDate.getTime() < 0) {\n        throw new Error('Out of the timespan range');\n      }\n    }\n\n    // Day of month and week matching:\n    //\n    // \"The day of a command's execution can be specified by two fields --\n    // day of month, and day of week.  If  both\t fields\t are  restricted  (ie,\n    // aren't  *),  the command will be run when either field matches the cur-\n    // rent time.  For example, \"30 4 1,15 * 5\" would cause a command to be\n    // run at 4:30 am on the  1st and 15th of each month, plus every Friday.\"\n    //\n    // http://unixhelp.ed.ac.uk/CGI/man-cgi?crontab+5\n    //\n\n    var dayOfMonthMatch = matchSchedule(currentDate.getDate(), this.fields.dayOfMonth);\n    if (isLInExpressions(this.fields.dayOfMonth)) {\n      dayOfMonthMatch = dayOfMonthMatch || currentDate.isLastDayOfMonth();\n    }\n    var dayOfWeekMatch = matchSchedule(currentDate.getDay(), this.fields.dayOfWeek);\n    if (isLInExpressions(this.fields.dayOfWeek)) {\n      dayOfWeekMatch = dayOfWeekMatch || isLastWeekdayOfMonthMatch(this.fields.dayOfWeek);\n    }\n    var isDayOfMonthWildcardMatch = this.fields.dayOfMonth.length >= CronExpression.daysInMonth[currentDate.getMonth()];\n    var isDayOfWeekWildcardMatch = this.fields.dayOfWeek.length === CronExpression.constraints[5].max - CronExpression.constraints[5].min + 1;\n    var currentHour = currentDate.getHours();\n\n    // Add or subtract day if select day not match with month (according to calendar)\n    if (!dayOfMonthMatch && (!dayOfWeekMatch || isDayOfWeekWildcardMatch)) {\n      this._applyTimezoneShift(currentDate, dateMathVerb, 'Day');\n      continue;\n    }\n\n    // Add or subtract day if not day of month is set (and no match) and day of week is wildcard\n    if (!isDayOfMonthWildcardMatch && isDayOfWeekWildcardMatch && !dayOfMonthMatch) {\n      this._applyTimezoneShift(currentDate, dateMathVerb, 'Day');\n      continue;\n    }\n\n    // Add or subtract day if not day of week is set (and no match) and day of month is wildcard\n    if (isDayOfMonthWildcardMatch && !isDayOfWeekWildcardMatch && !dayOfWeekMatch) {\n      this._applyTimezoneShift(currentDate, dateMathVerb, 'Day');\n      continue;\n    }\n\n    // Add or subtract day if day of week & nthDayOfWeek are set (and no match)\n    if (this._nthDayOfWeek > 0 && !isNthDayMatch(currentDate, this._nthDayOfWeek)) {\n      this._applyTimezoneShift(currentDate, dateMathVerb, 'Day');\n      continue;\n    }\n\n    // Match month\n    if (!matchSchedule(currentDate.getMonth() + 1, this.fields.month)) {\n      this._applyTimezoneShift(currentDate, dateMathVerb, 'Month');\n      continue;\n    }\n\n    // Match hour\n    if (!matchSchedule(currentHour, this.fields.hour)) {\n      if (this._dstStart !== currentHour) {\n        this._dstStart = null;\n        this._applyTimezoneShift(currentDate, dateMathVerb, 'Hour');\n        continue;\n      } else if (!matchSchedule(currentHour - 1, this.fields.hour)) {\n        currentDate[dateMathVerb + 'Hour']();\n        continue;\n      }\n    } else if (this._dstEnd === currentHour) {\n      if (!reverse) {\n        this._dstEnd = null;\n        this._applyTimezoneShift(currentDate, 'add', 'Hour');\n        continue;\n      }\n    }\n\n    // Match minute\n    if (!matchSchedule(currentDate.getMinutes(), this.fields.minute)) {\n      this._applyTimezoneShift(currentDate, dateMathVerb, 'Minute');\n      continue;\n    }\n\n    // Match second\n    if (!matchSchedule(currentDate.getSeconds(), this.fields.second)) {\n      this._applyTimezoneShift(currentDate, dateMathVerb, 'Second');\n      continue;\n    }\n\n    // Increase a second in case in the first iteration the currentDate was not\n    // modified\n    if (startTimestamp === currentDate.getTime()) {\n      if (dateMathVerb === 'add' || currentDate.getMilliseconds() === 0) {\n        this._applyTimezoneShift(currentDate, dateMathVerb, 'Second');\n      } else {\n        currentDate.setMilliseconds(0);\n      }\n      continue;\n    }\n    break;\n  }\n  if (stepCount >= LOOP_LIMIT) {\n    throw new Error('Invalid expression, loop limit exceeded');\n  }\n  this._currentDate = new CronDate(currentDate, this._tz);\n  this._hasIterated = true;\n  return currentDate;\n};\n\n/**\n * Find next suitable date\n *\n * @public\n * @return {CronDate|Object}\n */\nCronExpression.prototype.next = function next() {\n  var schedule = this._findSchedule();\n\n  // Try to return ES6 compatible iterator\n  if (this._isIterator) {\n    return {\n      value: schedule,\n      done: !this.hasNext()\n    };\n  }\n  return schedule;\n};\n\n/**\n * Find previous suitable date\n *\n * @public\n * @return {CronDate|Object}\n */\nCronExpression.prototype.prev = function prev() {\n  var schedule = this._findSchedule(true);\n\n  // Try to return ES6 compatible iterator\n  if (this._isIterator) {\n    return {\n      value: schedule,\n      done: !this.hasPrev()\n    };\n  }\n  return schedule;\n};\n\n/**\n * Check if next suitable date exists\n *\n * @public\n * @return {Boolean}\n */\nCronExpression.prototype.hasNext = function () {\n  var current = this._currentDate;\n  var hasIterated = this._hasIterated;\n  try {\n    this._findSchedule();\n    return true;\n  } catch (err) {\n    return false;\n  } finally {\n    this._currentDate = current;\n    this._hasIterated = hasIterated;\n  }\n};\n\n/**\n * Check if previous suitable date exists\n *\n * @public\n * @return {Boolean}\n */\nCronExpression.prototype.hasPrev = function () {\n  var current = this._currentDate;\n  var hasIterated = this._hasIterated;\n  try {\n    this._findSchedule(true);\n    return true;\n  } catch (err) {\n    return false;\n  } finally {\n    this._currentDate = current;\n    this._hasIterated = hasIterated;\n  }\n};\n\n/**\n * Iterate over expression iterator\n *\n * @public\n * @param {Number} steps Numbers of steps to iterate\n * @param {Function} callback Optional callback\n * @return {Array} Array of the iterated results\n */\nCronExpression.prototype.iterate = function iterate(steps, callback) {\n  var dates = [];\n  if (steps >= 0) {\n    for (var i = 0, c = steps; i < c; i++) {\n      try {\n        var item = this.next();\n        dates.push(item);\n\n        // Fire the callback\n        if (callback) {\n          callback(item, i);\n        }\n      } catch (err) {\n        break;\n      }\n    }\n  } else {\n    for (var i = 0, c = steps; i > c; i--) {\n      try {\n        var item = this.prev();\n        dates.push(item);\n\n        // Fire the callback\n        if (callback) {\n          callback(item, i);\n        }\n      } catch (err) {\n        break;\n      }\n    }\n  }\n  return dates;\n};\n\n/**\n * Reset expression iterator state\n *\n * @public\n */\nCronExpression.prototype.reset = function reset(newDate) {\n  this._currentDate = new CronDate(newDate || this._options.currentDate);\n};\n\n/**\n * Stringify the expression\n *\n * @public\n * @param {Boolean} [includeSeconds] Should stringify seconds\n * @return {String}\n */\nCronExpression.prototype.stringify = function stringify(includeSeconds) {\n  var resultArr = [];\n  for (var i = includeSeconds ? 0 : 1, c = CronExpression.map.length; i < c; ++i) {\n    var field = CronExpression.map[i];\n    var value = this.fields[field];\n    var constraint = CronExpression.constraints[i];\n    if (field === 'dayOfMonth' && this.fields.month.length === 1) {\n      constraint = {\n        min: 1,\n        max: CronExpression.daysInMonth[this.fields.month[0] - 1]\n      };\n    } else if (field === 'dayOfWeek') {\n      // Prefer 0-6 range when serializing day of week field\n      constraint = {\n        min: 0,\n        max: 6\n      };\n      value = value[value.length - 1] === 7 ? value.slice(0, -1) : value;\n    }\n    resultArr.push(stringifyField(value, constraint.min, constraint.max));\n  }\n  return resultArr.join(' ');\n};\n\n/**\n * Parse input expression (async)\n *\n * @public\n * @param {String} expression Input expression\n * @param {Object} [options] Parsing options\n */\nCronExpression.parse = function parse(expression, options) {\n  var self = this;\n  if (typeof options === 'function') {\n    options = {};\n  }\n  function parse(expression, options) {\n    if (!options) {\n      options = {};\n    }\n    if (typeof options.currentDate === 'undefined') {\n      options.currentDate = new CronDate(undefined, self._tz);\n    }\n\n    // Is input expression predefined?\n    if (CronExpression.predefined[expression]) {\n      expression = CronExpression.predefined[expression];\n    }\n\n    // Split fields\n    var fields = [];\n    var atoms = (expression + '').trim().split(/\\s+/);\n    if (atoms.length > 6) {\n      throw new Error('Invalid cron expression');\n    }\n\n    // Resolve fields\n    var start = CronExpression.map.length - atoms.length;\n    for (var i = 0, c = CronExpression.map.length; i < c; ++i) {\n      var field = CronExpression.map[i]; // Field name\n      var value = atoms[atoms.length > c ? i : i - start]; // Field value\n\n      if (i < start || !value) {\n        // Use default value\n        fields.push(CronExpression._parseField(field, CronExpression.parseDefaults[i], CronExpression.constraints[i]));\n      } else {\n        var val = field === 'dayOfWeek' ? parseNthDay(value) : value;\n        fields.push(CronExpression._parseField(field, val, CronExpression.constraints[i]));\n      }\n    }\n    var mappedFields = {};\n    for (var i = 0, c = CronExpression.map.length; i < c; i++) {\n      var key = CronExpression.map[i];\n      mappedFields[key] = fields[i];\n    }\n    var dayOfMonth = CronExpression._handleMaxDaysInMonth(mappedFields);\n    mappedFields.dayOfMonth = dayOfMonth || mappedFields.dayOfMonth;\n    return new CronExpression(mappedFields, options);\n\n    /**\n     * Parses out the # special character for the dayOfWeek field & adds it to options.\n     *\n     * @param {String} val\n     * @return {String}\n     * @private\n     */\n    function parseNthDay(val) {\n      var atoms = val.split('#');\n      if (atoms.length > 1) {\n        var nthValue = +atoms[atoms.length - 1];\n        if (/,/.test(val)) {\n          throw new Error('Constraint error, invalid dayOfWeek `#` and `,` ' + 'special characters are incompatible');\n        }\n        if (/\\//.test(val)) {\n          throw new Error('Constraint error, invalid dayOfWeek `#` and `/` ' + 'special characters are incompatible');\n        }\n        if (/-/.test(val)) {\n          throw new Error('Constraint error, invalid dayOfWeek `#` and `-` ' + 'special characters are incompatible');\n        }\n        if (atoms.length > 2 || Number.isNaN(nthValue) || nthValue < 1 || nthValue > 5) {\n          throw new Error('Constraint error, invalid dayOfWeek occurrence number (#)');\n        }\n        options.nthDayOfWeek = nthValue;\n        return atoms[0];\n      }\n      return val;\n    }\n  }\n  return parse(expression, options);\n};\n\n/**\n * Convert cron fields back to Cron Expression\n *\n * @public\n * @param {Object} fields Input fields\n * @param {Object} [options] Parsing options\n * @return {Object}\n */\nCronExpression.fieldsToExpression = function fieldsToExpression(fields, options) {\n  function validateConstraints(field, values, constraints) {\n    if (!values) {\n      throw new Error('Validation error, Field ' + field + ' is missing');\n    }\n    if (values.length === 0) {\n      throw new Error('Validation error, Field ' + field + ' contains no values');\n    }\n    for (var i = 0, c = values.length; i < c; i++) {\n      var value = values[i];\n      if (CronExpression._isValidConstraintChar(constraints, value)) {\n        continue;\n      }\n\n      // Check constraints\n      if (typeof value !== 'number' || Number.isNaN(value) || value < constraints.min || value > constraints.max) {\n        throw new Error('Constraint error, got value ' + value + ' expected range ' + constraints.min + '-' + constraints.max);\n      }\n    }\n  }\n  var mappedFields = {};\n  for (var i = 0, c = CronExpression.map.length; i < c; ++i) {\n    var field = CronExpression.map[i]; // Field name\n    var values = fields[field];\n    validateConstraints(field, values, CronExpression.constraints[i]);\n    var copy = [];\n    var j = -1;\n    while (++j < values.length) {\n      copy[j] = values[j];\n    }\n    values = copy.sort(CronExpression._sortCompareFn).filter(function (item, pos, ary) {\n      return !pos || item !== ary[pos - 1];\n    });\n    if (values.length !== copy.length) {\n      throw new Error('Validation error, Field ' + field + ' contains duplicate values');\n    }\n    mappedFields[field] = values;\n  }\n  var dayOfMonth = CronExpression._handleMaxDaysInMonth(mappedFields);\n  mappedFields.dayOfMonth = dayOfMonth || mappedFields.dayOfMonth;\n  return new CronExpression(mappedFields, options || {});\n};\nmodule.exports = CronExpression;", "map": {"version": 3, "names": ["CronDate", "require", "stringifyField", "LOOP_LIMIT", "CronExpression", "fields", "options", "_options", "_utc", "utc", "_tz", "tz", "_currentDate", "currentDate", "_startDate", "startDate", "_endDate", "endDate", "_isIterator", "iterator", "_hasIterated", "_nthDayOfWeek", "nthDayOfWeek", "_freezeFields", "map", "predefined", "constraints", "min", "max", "chars", "daysInMonth", "aliases", "month", "jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec", "dayOfWeek", "sun", "mon", "tue", "wed", "thu", "fri", "sat", "parseDefaults", "standardValidCharacters", "dayOfWeekValidCharacters", "dayOfMonthValidCharacters", "validCharacters", "second", "minute", "hour", "dayOfMonth", "_isValidConstraintChar", "value", "some", "char", "indexOf", "_parseField", "field", "replace", "match", "toLowerCase", "Error", "test", "parseSequence", "val", "stack", "handleResult", "result", "Array", "i", "c", "length", "push", "Number", "isNaN", "numResult", "atoms", "split", "every", "atom", "parseRepeat", "sort", "_sortCompareFn", "repeatInterval", "parseRange", "repeatIndex", "index", "count", "exists", "a", "b", "aIsNumber", "bIsNumber", "localeCompare", "_handleMaxDaysInMonth", "<PERSON><PERSON><PERSON>s", "filter", "Object", "freeze", "prototype", "_applyTimezoneShift", "dateMathVerb", "method", "prevTime", "getTime", "currTime", "getMinutes", "getSeconds", "addHour", "subtractHour", "previousHour", "getHours", "currentHour", "diff", "_dstStart", "_dstEnd", "_findSchedule", "reverse", "matchSchedule", "sequence", "isNthDayMatch", "date", "getDate", "offset", "adjustedDate", "occurrence", "Math", "floor", "isLInExpressions", "expressions", "expression", "startTimestamp", "stepCount", "isLastWeekdayOfMonthMatch", "weekday", "parseInt", "getDay", "isLastWeekdayOfMonth", "dayOfMonthMatch", "isLastDayOfMonth", "dayOfWeekMatch", "isDayOfMonthWildcardMatch", "getMonth", "isDayOfWeekWildcardMatch", "getMilliseconds", "setMilliseconds", "next", "schedule", "done", "hasNext", "prev", "has<PERSON>rev", "current", "hasIterated", "err", "iterate", "steps", "callback", "dates", "item", "reset", "newDate", "stringify", "includeSeconds", "resultArr", "constraint", "slice", "join", "parse", "self", "undefined", "trim", "start", "parseNthDay", "key", "nthValue", "fieldsToExpression", "validateConstraints", "values", "copy", "j", "pos", "ary", "module", "exports"], "sources": ["C:/Users/<USER>/Documents/ekrani/node_modules/cron-parser/lib/expression.js"], "sourcesContent": ["'use strict';\n\n// Load Date class extensions\nvar CronDate = require('./date');\n\nvar stringifyField = require('./field_stringify');\n\n/**\n * Cron iteration loop safety limit\n */\nvar LOOP_LIMIT = 10000;\n\n/**\n * Construct a new expression parser\n *\n * Options:\n *   currentDate: iterator start date\n *   endDate: iterator end date\n *\n * @constructor\n * @private\n * @param {Object} fields  Expression fields parsed values\n * @param {Object} options Parser options\n */\nfunction CronExpression (fields, options) {\n  this._options = options;\n  this._utc = options.utc || false;\n  this._tz = this._utc ? 'UTC' : options.tz;\n  this._currentDate = new CronDate(options.currentDate, this._tz);\n  this._startDate = options.startDate ? new CronDate(options.startDate, this._tz) : null;\n  this._endDate = options.endDate ? new CronDate(options.endDate, this._tz) : null;\n  this._isIterator = options.iterator || false;\n  this._hasIterated = false;\n  this._nthDayOfWeek = options.nthDayOfWeek || 0;\n  this.fields = CronExpression._freezeFields(fields);\n}\n\n/**\n * Field mappings\n * @type {Array}\n */\nCronExpression.map = [ 'second', 'minute', 'hour', 'dayOfMonth', 'month', 'dayOfWeek' ];\n\n/**\n * Prefined intervals\n * @type {Object}\n */\nCronExpression.predefined = {\n  '@yearly': '0 0 1 1 *',\n  '@monthly': '0 0 1 * *',\n  '@weekly': '0 0 * * 0',\n  '@daily': '0 0 * * *',\n  '@hourly': '0 * * * *'\n};\n\n/**\n * Fields constraints\n * @type {Array}\n */\nCronExpression.constraints = [\n  { min: 0, max: 59, chars: [] }, // Second\n  { min: 0, max: 59, chars: [] }, // Minute\n  { min: 0, max: 23, chars: [] }, // Hour\n  { min: 1, max: 31, chars: ['L'] }, // Day of month\n  { min: 1, max: 12, chars: [] }, // Month\n  { min: 0, max: 7, chars: ['L'] }, // Day of week\n];\n\n/**\n * Days in month\n * @type {number[]}\n */\nCronExpression.daysInMonth = [\n  31,\n  29,\n  31,\n  30,\n  31,\n  30,\n  31,\n  31,\n  30,\n  31,\n  30,\n  31\n];\n\n/**\n * Field aliases\n * @type {Object}\n */\nCronExpression.aliases = {\n  month: {\n    jan: 1,\n    feb: 2,\n    mar: 3,\n    apr: 4,\n    may: 5,\n    jun: 6,\n    jul: 7,\n    aug: 8,\n    sep: 9,\n    oct: 10,\n    nov: 11,\n    dec: 12\n  },\n\n  dayOfWeek: {\n    sun: 0,\n    mon: 1,\n    tue: 2,\n    wed: 3,\n    thu: 4,\n    fri: 5,\n    sat: 6\n  }\n};\n\n/**\n * Field defaults\n * @type {Array}\n */\nCronExpression.parseDefaults = [ '0', '*', '*', '*', '*', '*' ];\n\nCronExpression.standardValidCharacters = /^[,*\\d/-]+$/;\nCronExpression.dayOfWeekValidCharacters = /^[?,*\\dL#/-]+$/;\nCronExpression.dayOfMonthValidCharacters = /^[?,*\\dL/-]+$/;\nCronExpression.validCharacters = {\n  second: CronExpression.standardValidCharacters,\n  minute: CronExpression.standardValidCharacters,\n  hour: CronExpression.standardValidCharacters,\n  dayOfMonth: CronExpression.dayOfMonthValidCharacters,\n  month: CronExpression.standardValidCharacters,\n  dayOfWeek: CronExpression.dayOfWeekValidCharacters,\n};\n\nCronExpression._isValidConstraintChar = function _isValidConstraintChar(constraints, value) {\n  if (typeof value !== 'string') {\n    return false;\n  }\n\n  return constraints.chars.some(function(char) {\n    return value.indexOf(char) > -1;\n  });\n};\n\n/**\n * Parse input interval\n *\n * @param {String} field Field symbolic name\n * @param {String} value Field value\n * @param {Array} constraints Range upper and lower constraints\n * @return {Array} Sequence of sorted values\n * @private\n */\nCronExpression._parseField = function _parseField (field, value, constraints) {\n  // Replace aliases\n  switch (field) {\n    case 'month':\n    case 'dayOfWeek':\n      var aliases = CronExpression.aliases[field];\n\n      value = value.replace(/[a-z]{3}/gi, function(match) {\n        match = match.toLowerCase();\n\n        if (typeof aliases[match] !== 'undefined') {\n          return aliases[match];\n        } else {\n          throw new Error('Validation error, cannot resolve alias \"' + match + '\"');\n        }\n      });\n      break;\n  }\n\n  // Check for valid characters.\n  if (!(CronExpression.validCharacters[field].test(value))) {\n    throw new Error('Invalid characters, got value: ' + value);\n  }\n\n  // Replace '*' and '?'\n  if (value.indexOf('*') !== -1) {\n    value = value.replace(/\\*/g, constraints.min + '-' + constraints.max);\n  } else if (value.indexOf('?') !== -1) {\n    value = value.replace(/\\?/g, constraints.min + '-' + constraints.max);\n  }\n\n  //\n  // Inline parsing functions\n  //\n  // Parser path:\n  //  - parseSequence\n  //    - parseRepeat\n  //      - parseRange\n\n  /**\n   * Parse sequence\n   *\n   * @param {String} val\n   * @return {Array}\n   * @private\n   */\n  function parseSequence (val) {\n    var stack = [];\n\n    function handleResult (result) {\n      if (result instanceof Array) { // Make sequence linear\n        for (var i = 0, c = result.length; i < c; i++) {\n          var value = result[i];\n\n          if (CronExpression._isValidConstraintChar(constraints, value)) {\n            stack.push(value);\n            continue;\n          }\n          // Check constraints\n          if (typeof value !== 'number' || Number.isNaN(value) || value < constraints.min || value > constraints.max) {\n            throw new Error(\n                'Constraint error, got value ' + value + ' expected range ' +\n                constraints.min + '-' + constraints.max\n            );\n          }\n\n          stack.push(value);\n        }\n      } else { // Scalar value\n\n        if (CronExpression._isValidConstraintChar(constraints, result)) {\n          stack.push(result);\n          return;\n        }\n\n        var numResult = +result;\n\n        // Check constraints\n        if (Number.isNaN(numResult) || numResult < constraints.min || numResult > constraints.max) {\n          throw new Error(\n            'Constraint error, got value ' + result + ' expected range ' +\n            constraints.min + '-' + constraints.max\n          );\n        }\n\n        if (field === 'dayOfWeek') {\n          numResult = numResult % 7;\n        }\n\n        stack.push(numResult);\n      }\n    }\n\n    var atoms = val.split(',');\n    if (!atoms.every(function (atom) {\n      return atom.length > 0;\n    })) {\n      throw new Error('Invalid list value format');\n    }\n\n    if (atoms.length > 1) {\n      for (var i = 0, c = atoms.length; i < c; i++) {\n        handleResult(parseRepeat(atoms[i]));\n      }\n    } else {\n      handleResult(parseRepeat(val));\n    }\n\n    stack.sort(CronExpression._sortCompareFn);\n\n    return stack;\n  }\n\n  /**\n   * Parse repetition interval\n   *\n   * @param {String} val\n   * @return {Array}\n   */\n  function parseRepeat (val) {\n    var repeatInterval = 1;\n    var atoms = val.split('/');\n\n    if (atoms.length > 2) {\n      throw new Error('Invalid repeat: ' + val);\n    }\n\n    if (atoms.length > 1) {\n      if (atoms[0] == +atoms[0]) {\n        atoms = [atoms[0] + '-' + constraints.max, atoms[1]];\n      }\n      return parseRange(atoms[0], atoms[atoms.length - 1]);\n    }\n\n    return parseRange(val, repeatInterval);\n  }\n\n  /**\n   * Parse range\n   *\n   * @param {String} val\n   * @param {Number} repeatInterval Repetition interval\n   * @return {Array}\n   * @private\n   */\n  function parseRange (val, repeatInterval) {\n    var stack = [];\n    var atoms = val.split('-');\n\n    if (atoms.length > 1 ) {\n      // Invalid range, return value\n      if (atoms.length < 2) {\n        return +val;\n      }\n\n      if (!atoms[0].length) {\n        if (!atoms[1].length) {\n          throw new Error('Invalid range: ' + val);\n        }\n\n        return +val;\n      }\n\n      // Validate range\n      var min = +atoms[0];\n      var max = +atoms[1];\n\n      if (Number.isNaN(min) || Number.isNaN(max) ||\n          min < constraints.min || max > constraints.max) {\n        throw new Error(\n          'Constraint error, got range ' +\n          min + '-' + max +\n          ' expected range ' +\n          constraints.min + '-' + constraints.max\n        );\n      } else if (min > max) {\n        throw new Error('Invalid range: ' + val);\n      }\n\n      // Create range\n      var repeatIndex = +repeatInterval;\n\n      if (Number.isNaN(repeatIndex) || repeatIndex <= 0) {\n        throw new Error('Constraint error, cannot repeat at every ' + repeatIndex + ' time.');\n      }\n\n      // JS DOW is in range of 0-6 (SUN-SAT) but we also support 7 in the expression\n      // Handle case when range contains 7 instead of 0 and translate this value to 0\n      if (field === 'dayOfWeek' && max % 7 === 0) {\n        stack.push(0);\n      }\n\n      for (var index = min, count = max; index <= count; index++) {\n        var exists = stack.indexOf(index) !== -1;\n        if (!exists && repeatIndex > 0 && (repeatIndex % repeatInterval) === 0) {\n          repeatIndex = 1;\n          stack.push(index);\n        } else {\n          repeatIndex++;\n        }\n      }\n      return stack;\n    }\n\n    return Number.isNaN(+val) ? val : +val;\n  }\n\n  return parseSequence(value);\n};\n\nCronExpression._sortCompareFn = function(a, b) {\n  var aIsNumber = typeof a === 'number';\n  var bIsNumber = typeof b === 'number';\n\n  if (aIsNumber && bIsNumber) {\n    return a - b;\n  }\n\n  if (!aIsNumber && bIsNumber) {\n    return 1;\n  }\n\n  if (aIsNumber && !bIsNumber) {\n    return -1;\n  }\n\n  return a.localeCompare(b);\n};\n\nCronExpression._handleMaxDaysInMonth = function(mappedFields) {\n  // Filter out any day of month value that is larger than given month expects\n  if (mappedFields.month.length === 1) {\n    var daysInMonth = CronExpression.daysInMonth[mappedFields.month[0] - 1];\n\n    if (mappedFields.dayOfMonth[0] > daysInMonth) {\n      throw new Error('Invalid explicit day of month definition');\n    }\n\n    return mappedFields.dayOfMonth\n      .filter(function(dayOfMonth) {\n        return dayOfMonth === 'L' ? true : dayOfMonth <= daysInMonth;\n      })\n      .sort(CronExpression._sortCompareFn);\n  }\n};\n\nCronExpression._freezeFields = function(fields) {\n  for (var i = 0, c = CronExpression.map.length; i < c; ++i) {\n    var field = CronExpression.map[i]; // Field name\n    var value = fields[field];\n    fields[field] = Object.freeze(value);\n  }\n  return Object.freeze(fields);\n};\n\nCronExpression.prototype._applyTimezoneShift = function(currentDate, dateMathVerb, method) {\n  if ((method === 'Month') || (method === 'Day')) {\n    var prevTime = currentDate.getTime();\n    currentDate[dateMathVerb + method]();\n    var currTime = currentDate.getTime();\n    if (prevTime === currTime) {\n      // Jumped into a not existent date due to a DST transition\n      if ((currentDate.getMinutes() === 0) &&\n          (currentDate.getSeconds() === 0)) {\n        currentDate.addHour();\n      } else if ((currentDate.getMinutes() === 59) &&\n                 (currentDate.getSeconds() === 59)) {\n        currentDate.subtractHour();\n      }\n    }\n  } else {\n    var previousHour = currentDate.getHours();\n    currentDate[dateMathVerb + method]();\n    var currentHour = currentDate.getHours();\n    var diff = currentHour - previousHour;\n    if (diff === 2) {\n        // Starting DST\n        if (this.fields.hour.length !== 24) {\n          // Hour is specified\n          this._dstStart = currentHour;\n        }\n      } else if ((diff === 0) &&\n                 (currentDate.getMinutes() === 0) &&\n                 (currentDate.getSeconds() === 0)) {\n        // Ending DST\n        if (this.fields.hour.length !== 24) {\n          // Hour is specified\n          this._dstEnd = currentHour;\n        }\n      }\n  }\n};\n\n\n/**\n * Find next or previous matching schedule date\n *\n * @return {CronDate}\n * @private\n */\nCronExpression.prototype._findSchedule = function _findSchedule (reverse) {\n\n  /**\n   * Match field value\n   *\n   * @param {String} value\n   * @param {Array} sequence\n   * @return {Boolean}\n   * @private\n   */\n  function matchSchedule (value, sequence) {\n    for (var i = 0, c = sequence.length; i < c; i++) {\n      if (sequence[i] >= value) {\n        return sequence[i] === value;\n      }\n    }\n\n    return sequence[0] === value;\n  }\n\n  /**\n   * Helps determine if the provided date is the correct nth occurence of the\n   * desired day of week.\n   *\n   * @param {CronDate} date\n   * @param {Number} nthDayOfWeek\n   * @return {Boolean}\n   * @private\n   */\n  function isNthDayMatch(date, nthDayOfWeek) {\n    if (nthDayOfWeek < 6) {\n      if (\n        date.getDate() < 8 &&\n        nthDayOfWeek === 1 // First occurence has to happen in first 7 days of the month\n      ) {\n        return true;\n      }\n\n      var offset = date.getDate() % 7 ? 1 : 0; // Math is off by 1 when dayOfWeek isn't divisible by 7\n      var adjustedDate = date.getDate() - (date.getDate() % 7); // find the first occurance\n      var occurrence = Math.floor(adjustedDate / 7) + offset;\n\n      return occurrence === nthDayOfWeek;\n    }\n\n    return false;\n  }\n\n  /**\n   * Helper function that checks if 'L' is in the array\n   *\n   * @param {Array} expressions\n   */\n  function isLInExpressions(expressions) {\n    return expressions.length > 0 && expressions.some(function(expression) {\n      return typeof expression === 'string' && expression.indexOf('L') >= 0;\n    });\n  }\n\n\n  // Whether to use backwards directionality when searching\n  reverse = reverse || false;\n  var dateMathVerb = reverse ? 'subtract' : 'add';\n\n  var currentDate = new CronDate(this._currentDate, this._tz);\n  var startDate = this._startDate;\n  var endDate = this._endDate;\n\n  // Find matching schedule\n  var startTimestamp = currentDate.getTime();\n  var stepCount = 0;\n\n  function isLastWeekdayOfMonthMatch(expressions) {\n    return expressions.some(function(expression) {\n      // There might be multiple expressions and not all of them will contain\n      // the \"L\".\n      if (!isLInExpressions([expression])) {\n        return false;\n      }\n\n      // The first character represents the weekday\n      var weekday = Number.parseInt(expression[0]) % 7;\n\n      if (Number.isNaN(weekday)) {\n        throw new Error('Invalid last weekday of the month expression: ' + expression);\n      }\n\n      return currentDate.getDay() === weekday && currentDate.isLastWeekdayOfMonth();\n    });\n  }\n\n  while (stepCount < LOOP_LIMIT) {\n    stepCount++;\n\n    // Validate timespan\n    if (reverse) {\n      if (startDate && (currentDate.getTime() - startDate.getTime() < 0)) {\n        throw new Error('Out of the timespan range');\n      }\n    } else {\n      if (endDate && (endDate.getTime() - currentDate.getTime()) < 0) {\n        throw new Error('Out of the timespan range');\n      }\n    }\n\n    // Day of month and week matching:\n    //\n    // \"The day of a command's execution can be specified by two fields --\n    // day of month, and day of week.  If  both\t fields\t are  restricted  (ie,\n    // aren't  *),  the command will be run when either field matches the cur-\n    // rent time.  For example, \"30 4 1,15 * 5\" would cause a command to be\n    // run at 4:30 am on the  1st and 15th of each month, plus every Friday.\"\n    //\n    // http://unixhelp.ed.ac.uk/CGI/man-cgi?crontab+5\n    //\n\n    var dayOfMonthMatch = matchSchedule(currentDate.getDate(), this.fields.dayOfMonth);\n    if (isLInExpressions(this.fields.dayOfMonth)) {\n      dayOfMonthMatch = dayOfMonthMatch || currentDate.isLastDayOfMonth();\n    }\n    var dayOfWeekMatch = matchSchedule(currentDate.getDay(), this.fields.dayOfWeek);\n    if (isLInExpressions(this.fields.dayOfWeek)) {\n      dayOfWeekMatch = dayOfWeekMatch || isLastWeekdayOfMonthMatch(this.fields.dayOfWeek);\n    }\n    var isDayOfMonthWildcardMatch = this.fields.dayOfMonth.length >= CronExpression.daysInMonth[currentDate.getMonth()];\n    var isDayOfWeekWildcardMatch = this.fields.dayOfWeek.length === CronExpression.constraints[5].max - CronExpression.constraints[5].min + 1;\n    var currentHour = currentDate.getHours();\n\n    // Add or subtract day if select day not match with month (according to calendar)\n    if (!dayOfMonthMatch && (!dayOfWeekMatch || isDayOfWeekWildcardMatch)) {\n      this._applyTimezoneShift(currentDate, dateMathVerb, 'Day');\n      continue;\n    }\n\n    // Add or subtract day if not day of month is set (and no match) and day of week is wildcard\n    if (!isDayOfMonthWildcardMatch && isDayOfWeekWildcardMatch && !dayOfMonthMatch) {\n      this._applyTimezoneShift(currentDate, dateMathVerb, 'Day');\n      continue;\n    }\n\n    // Add or subtract day if not day of week is set (and no match) and day of month is wildcard\n    if (isDayOfMonthWildcardMatch && !isDayOfWeekWildcardMatch && !dayOfWeekMatch) {\n      this._applyTimezoneShift(currentDate, dateMathVerb, 'Day');\n      continue;\n    }\n\n    // Add or subtract day if day of week & nthDayOfWeek are set (and no match)\n    if (\n      this._nthDayOfWeek > 0 &&\n      !isNthDayMatch(currentDate, this._nthDayOfWeek)\n    ) {\n      this._applyTimezoneShift(currentDate, dateMathVerb, 'Day');\n      continue;\n    }\n\n    // Match month\n    if (!matchSchedule(currentDate.getMonth() + 1, this.fields.month)) {\n      this._applyTimezoneShift(currentDate, dateMathVerb, 'Month');\n      continue;\n    }\n\n    // Match hour\n    if (!matchSchedule(currentHour, this.fields.hour)) {\n      if (this._dstStart !== currentHour) {\n        this._dstStart = null;\n        this._applyTimezoneShift(currentDate, dateMathVerb, 'Hour');\n        continue;\n      } else if (!matchSchedule(currentHour - 1, this.fields.hour)) {\n        currentDate[dateMathVerb + 'Hour']();\n        continue;\n      }\n    } else if (this._dstEnd === currentHour) {\n      if (!reverse) {\n        this._dstEnd = null;\n        this._applyTimezoneShift(currentDate, 'add', 'Hour');\n        continue;\n      }\n    }\n\n    // Match minute\n    if (!matchSchedule(currentDate.getMinutes(), this.fields.minute)) {\n      this._applyTimezoneShift(currentDate, dateMathVerb, 'Minute');\n      continue;\n    }\n\n    // Match second\n    if (!matchSchedule(currentDate.getSeconds(), this.fields.second)) {\n      this._applyTimezoneShift(currentDate, dateMathVerb, 'Second');\n      continue;\n    }\n\n    // Increase a second in case in the first iteration the currentDate was not\n    // modified\n    if (startTimestamp === currentDate.getTime()) {\n      if ((dateMathVerb === 'add') || (currentDate.getMilliseconds() === 0)) {\n        this._applyTimezoneShift(currentDate, dateMathVerb, 'Second');\n      } else {\n        currentDate.setMilliseconds(0);\n      }\n\n      continue;\n    }\n\n    break;\n  }\n\n  if (stepCount >= LOOP_LIMIT) {\n    throw new Error('Invalid expression, loop limit exceeded');\n  }\n\n  this._currentDate = new CronDate(currentDate, this._tz);\n  this._hasIterated = true;\n\n  return currentDate;\n};\n\n/**\n * Find next suitable date\n *\n * @public\n * @return {CronDate|Object}\n */\nCronExpression.prototype.next = function next () {\n  var schedule = this._findSchedule();\n\n  // Try to return ES6 compatible iterator\n  if (this._isIterator) {\n    return {\n      value: schedule,\n      done: !this.hasNext()\n    };\n  }\n\n  return schedule;\n};\n\n/**\n * Find previous suitable date\n *\n * @public\n * @return {CronDate|Object}\n */\nCronExpression.prototype.prev = function prev () {\n  var schedule = this._findSchedule(true);\n\n  // Try to return ES6 compatible iterator\n  if (this._isIterator) {\n    return {\n      value: schedule,\n      done: !this.hasPrev()\n    };\n  }\n\n  return schedule;\n};\n\n/**\n * Check if next suitable date exists\n *\n * @public\n * @return {Boolean}\n */\nCronExpression.prototype.hasNext = function() {\n  var current = this._currentDate;\n  var hasIterated = this._hasIterated;\n\n  try {\n    this._findSchedule();\n    return true;\n  } catch (err) {\n    return false;\n  } finally {\n    this._currentDate = current;\n    this._hasIterated = hasIterated;\n  }\n};\n\n/**\n * Check if previous suitable date exists\n *\n * @public\n * @return {Boolean}\n */\nCronExpression.prototype.hasPrev = function() {\n  var current = this._currentDate;\n  var hasIterated = this._hasIterated;\n\n  try {\n    this._findSchedule(true);\n    return true;\n  } catch (err) {\n    return false;\n  } finally {\n    this._currentDate = current;\n    this._hasIterated = hasIterated;\n  }\n};\n\n/**\n * Iterate over expression iterator\n *\n * @public\n * @param {Number} steps Numbers of steps to iterate\n * @param {Function} callback Optional callback\n * @return {Array} Array of the iterated results\n */\nCronExpression.prototype.iterate = function iterate (steps, callback) {\n  var dates = [];\n\n  if (steps >= 0) {\n    for (var i = 0, c = steps; i < c; i++) {\n      try {\n        var item = this.next();\n        dates.push(item);\n\n        // Fire the callback\n        if (callback) {\n          callback(item, i);\n        }\n      } catch (err) {\n        break;\n      }\n    }\n  } else {\n    for (var i = 0, c = steps; i > c; i--) {\n      try {\n        var item = this.prev();\n        dates.push(item);\n\n        // Fire the callback\n        if (callback) {\n          callback(item, i);\n        }\n      } catch (err) {\n        break;\n      }\n    }\n  }\n\n  return dates;\n};\n\n/**\n * Reset expression iterator state\n *\n * @public\n */\nCronExpression.prototype.reset = function reset (newDate) {\n  this._currentDate = new CronDate(newDate || this._options.currentDate);\n};\n\n/**\n * Stringify the expression\n *\n * @public\n * @param {Boolean} [includeSeconds] Should stringify seconds\n * @return {String}\n */\nCronExpression.prototype.stringify = function stringify(includeSeconds) {\n  var resultArr = [];\n  for (var i = includeSeconds ? 0 : 1, c = CronExpression.map.length; i < c; ++i) {\n    var field = CronExpression.map[i];\n    var value = this.fields[field];\n    var constraint = CronExpression.constraints[i];\n\n    if (field === 'dayOfMonth' && this.fields.month.length === 1) {\n      constraint = { min: 1, max: CronExpression.daysInMonth[this.fields.month[0] - 1] };\n    } else if (field === 'dayOfWeek') {\n      // Prefer 0-6 range when serializing day of week field\n      constraint = { min: 0, max: 6 };\n      value = value[value.length - 1] === 7 ? value.slice(0, -1) : value;\n    }\n\n    resultArr.push(stringifyField(value, constraint.min, constraint.max));\n  }\n  return resultArr.join(' ');\n};\n\n/**\n * Parse input expression (async)\n *\n * @public\n * @param {String} expression Input expression\n * @param {Object} [options] Parsing options\n */\nCronExpression.parse = function parse(expression, options) {\n  var self = this;\n  if (typeof options === 'function') {\n    options = {};\n  }\n\n  function parse (expression, options) {\n    if (!options) {\n      options = {};\n    }\n\n    if (typeof options.currentDate === 'undefined') {\n      options.currentDate = new CronDate(undefined, self._tz);\n    }\n\n    // Is input expression predefined?\n    if (CronExpression.predefined[expression]) {\n      expression = CronExpression.predefined[expression];\n    }\n\n    // Split fields\n    var fields = [];\n    var atoms = (expression + '').trim().split(/\\s+/);\n\n    if (atoms.length > 6) {\n      throw new Error('Invalid cron expression');\n    }\n\n    // Resolve fields\n    var start = (CronExpression.map.length - atoms.length);\n    for (var i = 0, c = CronExpression.map.length; i < c; ++i) {\n      var field = CronExpression.map[i]; // Field name\n      var value = atoms[atoms.length > c ? i : i - start]; // Field value\n\n      if (i < start || !value) { // Use default value\n        fields.push(CronExpression._parseField(\n          field,\n          CronExpression.parseDefaults[i],\n          CronExpression.constraints[i]\n          )\n        );\n      } else {\n        var val = field === 'dayOfWeek' ? parseNthDay(value) : value;\n\n        fields.push(CronExpression._parseField(\n          field,\n          val,\n          CronExpression.constraints[i]\n          )\n        );\n      }\n    }\n\n    var mappedFields = {};\n    for (var i = 0, c = CronExpression.map.length; i < c; i++) {\n      var key = CronExpression.map[i];\n      mappedFields[key] = fields[i];\n    }\n\n    var dayOfMonth = CronExpression._handleMaxDaysInMonth(mappedFields);\n    mappedFields.dayOfMonth = dayOfMonth || mappedFields.dayOfMonth;\n    return new CronExpression(mappedFields, options);\n\n    /**\n     * Parses out the # special character for the dayOfWeek field & adds it to options.\n     *\n     * @param {String} val\n     * @return {String}\n     * @private\n     */\n    function parseNthDay(val) {\n      var atoms = val.split('#');\n      if (atoms.length > 1) {\n        var nthValue = +atoms[atoms.length - 1];\n        if(/,/.test(val)) {\n          throw new Error('Constraint error, invalid dayOfWeek `#` and `,` '\n            + 'special characters are incompatible');\n        }\n        if(/\\//.test(val)) {\n          throw new Error('Constraint error, invalid dayOfWeek `#` and `/` '\n            + 'special characters are incompatible');\n        }\n        if(/-/.test(val)) {\n          throw new Error('Constraint error, invalid dayOfWeek `#` and `-` '\n            + 'special characters are incompatible');\n        }\n        if (atoms.length > 2 || Number.isNaN(nthValue) || (nthValue < 1 || nthValue > 5)) {\n          throw new Error('Constraint error, invalid dayOfWeek occurrence number (#)');\n        }\n\n        options.nthDayOfWeek = nthValue;\n        return atoms[0];\n      }\n      return val;\n    }\n  }\n\n  return parse(expression, options);\n};\n\n/**\n * Convert cron fields back to Cron Expression\n *\n * @public\n * @param {Object} fields Input fields\n * @param {Object} [options] Parsing options\n * @return {Object}\n */\nCronExpression.fieldsToExpression = function fieldsToExpression(fields, options) {\n  function validateConstraints (field, values, constraints) {\n    if (!values) {\n      throw new Error('Validation error, Field ' + field + ' is missing');\n    }\n    if (values.length === 0) {\n      throw new Error('Validation error, Field ' + field + ' contains no values');\n    }\n    for (var i = 0, c = values.length; i < c; i++) {\n      var value = values[i];\n\n      if (CronExpression._isValidConstraintChar(constraints, value)) {\n        continue;\n      }\n\n      // Check constraints\n      if (typeof value !== 'number' || Number.isNaN(value) || value < constraints.min || value > constraints.max) {\n        throw new Error(\n          'Constraint error, got value ' + value + ' expected range ' +\n          constraints.min + '-' + constraints.max\n        );\n      }\n    }\n  }\n\n  var mappedFields = {};\n  for (var i = 0, c = CronExpression.map.length; i < c; ++i) {\n    var field = CronExpression.map[i]; // Field name\n    var values = fields[field];\n    validateConstraints(\n      field,\n      values,\n      CronExpression.constraints[i]\n    );\n    var copy = [];\n    var j = -1;\n    while (++j < values.length) {\n      copy[j] = values[j];\n    }\n    values = copy.sort(CronExpression._sortCompareFn)\n      .filter(function(item, pos, ary) {\n        return !pos || item !== ary[pos - 1];\n      });\n    if (values.length !== copy.length) {\n      throw new Error('Validation error, Field ' + field + ' contains duplicate values');\n    }\n    mappedFields[field] = values;\n  }\n  var dayOfMonth = CronExpression._handleMaxDaysInMonth(mappedFields);\n  mappedFields.dayOfMonth = dayOfMonth || mappedFields.dayOfMonth;\n  return new CronExpression(mappedFields, options || {});\n};\n\nmodule.exports = CronExpression;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAEhC,IAAIC,cAAc,GAAGD,OAAO,CAAC,mBAAmB,CAAC;;AAEjD;AACA;AACA;AACA,IAAIE,UAAU,GAAG,KAAK;;AAEtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAAEC,MAAM,EAAEC,OAAO,EAAE;EACxC,IAAI,CAACC,QAAQ,GAAGD,OAAO;EACvB,IAAI,CAACE,IAAI,GAAGF,OAAO,CAACG,GAAG,IAAI,KAAK;EAChC,IAAI,CAACC,GAAG,GAAG,IAAI,CAACF,IAAI,GAAG,KAAK,GAAGF,OAAO,CAACK,EAAE;EACzC,IAAI,CAACC,YAAY,GAAG,IAAIZ,QAAQ,CAACM,OAAO,CAACO,WAAW,EAAE,IAAI,CAACH,GAAG,CAAC;EAC/D,IAAI,CAACI,UAAU,GAAGR,OAAO,CAACS,SAAS,GAAG,IAAIf,QAAQ,CAACM,OAAO,CAACS,SAAS,EAAE,IAAI,CAACL,GAAG,CAAC,GAAG,IAAI;EACtF,IAAI,CAACM,QAAQ,GAAGV,OAAO,CAACW,OAAO,GAAG,IAAIjB,QAAQ,CAACM,OAAO,CAACW,OAAO,EAAE,IAAI,CAACP,GAAG,CAAC,GAAG,IAAI;EAChF,IAAI,CAACQ,WAAW,GAAGZ,OAAO,CAACa,QAAQ,IAAI,KAAK;EAC5C,IAAI,CAACC,YAAY,GAAG,KAAK;EACzB,IAAI,CAACC,aAAa,GAAGf,OAAO,CAACgB,YAAY,IAAI,CAAC;EAC9C,IAAI,CAACjB,MAAM,GAAGD,cAAc,CAACmB,aAAa,CAAClB,MAAM,CAAC;AACpD;;AAEA;AACA;AACA;AACA;AACAD,cAAc,CAACoB,GAAG,GAAG,CAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,CAAE;;AAEvF;AACA;AACA;AACA;AACApB,cAAc,CAACqB,UAAU,GAAG;EAC1B,SAAS,EAAE,WAAW;EACtB,UAAU,EAAE,WAAW;EACvB,SAAS,EAAE,WAAW;EACtB,QAAQ,EAAE,WAAW;EACrB,SAAS,EAAE;AACb,CAAC;;AAED;AACA;AACA;AACA;AACArB,cAAc,CAACsB,WAAW,GAAG,CAC3B;EAAEC,GAAG,EAAE,CAAC;EAAEC,GAAG,EAAE,EAAE;EAAEC,KAAK,EAAE;AAAG,CAAC;AAAE;AAChC;EAAEF,GAAG,EAAE,CAAC;EAAEC,GAAG,EAAE,EAAE;EAAEC,KAAK,EAAE;AAAG,CAAC;AAAE;AAChC;EAAEF,GAAG,EAAE,CAAC;EAAEC,GAAG,EAAE,EAAE;EAAEC,KAAK,EAAE;AAAG,CAAC;AAAE;AAChC;EAAEF,GAAG,EAAE,CAAC;EAAEC,GAAG,EAAE,EAAE;EAAEC,KAAK,EAAE,CAAC,GAAG;AAAE,CAAC;AAAE;AACnC;EAAEF,GAAG,EAAE,CAAC;EAAEC,GAAG,EAAE,EAAE;EAAEC,KAAK,EAAE;AAAG,CAAC;AAAE;AAChC;EAAEF,GAAG,EAAE,CAAC;EAAEC,GAAG,EAAE,CAAC;EAAEC,KAAK,EAAE,CAAC,GAAG;AAAE,CAAC,CAAE;AAAA,CACnC;;AAED;AACA;AACA;AACA;AACAzB,cAAc,CAAC0B,WAAW,GAAG,CAC3B,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,CACH;;AAED;AACA;AACA;AACA;AACA1B,cAAc,CAAC2B,OAAO,GAAG;EACvBC,KAAK,EAAE;IACLC,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE,EAAE;IACPC,GAAG,EAAE,EAAE;IACPC,GAAG,EAAE;EACP,CAAC;EAEDC,SAAS,EAAE;IACTC,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE;EACP;AACF,CAAC;;AAED;AACA;AACA;AACA;AACAhD,cAAc,CAACiD,aAAa,GAAG,CAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAE;AAE/DjD,cAAc,CAACkD,uBAAuB,GAAG,aAAa;AACtDlD,cAAc,CAACmD,wBAAwB,GAAG,gBAAgB;AAC1DnD,cAAc,CAACoD,yBAAyB,GAAG,eAAe;AAC1DpD,cAAc,CAACqD,eAAe,GAAG;EAC/BC,MAAM,EAAEtD,cAAc,CAACkD,uBAAuB;EAC9CK,MAAM,EAAEvD,cAAc,CAACkD,uBAAuB;EAC9CM,IAAI,EAAExD,cAAc,CAACkD,uBAAuB;EAC5CO,UAAU,EAAEzD,cAAc,CAACoD,yBAAyB;EACpDxB,KAAK,EAAE5B,cAAc,CAACkD,uBAAuB;EAC7CT,SAAS,EAAEzC,cAAc,CAACmD;AAC5B,CAAC;AAEDnD,cAAc,CAAC0D,sBAAsB,GAAG,SAASA,sBAAsBA,CAACpC,WAAW,EAAEqC,KAAK,EAAE;EAC1F,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAO,KAAK;EACd;EAEA,OAAOrC,WAAW,CAACG,KAAK,CAACmC,IAAI,CAAC,UAASC,IAAI,EAAE;IAC3C,OAAOF,KAAK,CAACG,OAAO,CAACD,IAAI,CAAC,GAAG,CAAC,CAAC;EACjC,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA7D,cAAc,CAAC+D,WAAW,GAAG,SAASA,WAAWA,CAAEC,KAAK,EAAEL,KAAK,EAAErC,WAAW,EAAE;EAC5E;EACA,QAAQ0C,KAAK;IACX,KAAK,OAAO;IACZ,KAAK,WAAW;MACd,IAAIrC,OAAO,GAAG3B,cAAc,CAAC2B,OAAO,CAACqC,KAAK,CAAC;MAE3CL,KAAK,GAAGA,KAAK,CAACM,OAAO,CAAC,YAAY,EAAE,UAASC,KAAK,EAAE;QAClDA,KAAK,GAAGA,KAAK,CAACC,WAAW,CAAC,CAAC;QAE3B,IAAI,OAAOxC,OAAO,CAACuC,KAAK,CAAC,KAAK,WAAW,EAAE;UACzC,OAAOvC,OAAO,CAACuC,KAAK,CAAC;QACvB,CAAC,MAAM;UACL,MAAM,IAAIE,KAAK,CAAC,0CAA0C,GAAGF,KAAK,GAAG,GAAG,CAAC;QAC3E;MACF,CAAC,CAAC;MACF;EACJ;;EAEA;EACA,IAAI,CAAElE,cAAc,CAACqD,eAAe,CAACW,KAAK,CAAC,CAACK,IAAI,CAACV,KAAK,CAAE,EAAE;IACxD,MAAM,IAAIS,KAAK,CAAC,iCAAiC,GAAGT,KAAK,CAAC;EAC5D;;EAEA;EACA,IAAIA,KAAK,CAACG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IAC7BH,KAAK,GAAGA,KAAK,CAACM,OAAO,CAAC,KAAK,EAAE3C,WAAW,CAACC,GAAG,GAAG,GAAG,GAAGD,WAAW,CAACE,GAAG,CAAC;EACvE,CAAC,MAAM,IAAImC,KAAK,CAACG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IACpCH,KAAK,GAAGA,KAAK,CAACM,OAAO,CAAC,KAAK,EAAE3C,WAAW,CAACC,GAAG,GAAG,GAAG,GAAGD,WAAW,CAACE,GAAG,CAAC;EACvE;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAAS8C,aAAaA,CAAEC,GAAG,EAAE;IAC3B,IAAIC,KAAK,GAAG,EAAE;IAEd,SAASC,YAAYA,CAAEC,MAAM,EAAE;MAC7B,IAAIA,MAAM,YAAYC,KAAK,EAAE;QAAE;QAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;UAC7C,IAAIjB,KAAK,GAAGe,MAAM,CAACE,CAAC,CAAC;UAErB,IAAI5E,cAAc,CAAC0D,sBAAsB,CAACpC,WAAW,EAAEqC,KAAK,CAAC,EAAE;YAC7Da,KAAK,CAACO,IAAI,CAACpB,KAAK,CAAC;YACjB;UACF;UACA;UACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIqB,MAAM,CAACC,KAAK,CAACtB,KAAK,CAAC,IAAIA,KAAK,GAAGrC,WAAW,CAACC,GAAG,IAAIoC,KAAK,GAAGrC,WAAW,CAACE,GAAG,EAAE;YAC1G,MAAM,IAAI4C,KAAK,CACX,8BAA8B,GAAGT,KAAK,GAAG,kBAAkB,GAC3DrC,WAAW,CAACC,GAAG,GAAG,GAAG,GAAGD,WAAW,CAACE,GACxC,CAAC;UACH;UAEAgD,KAAK,CAACO,IAAI,CAACpB,KAAK,CAAC;QACnB;MACF,CAAC,MAAM;QAAE;;QAEP,IAAI3D,cAAc,CAAC0D,sBAAsB,CAACpC,WAAW,EAAEoD,MAAM,CAAC,EAAE;UAC9DF,KAAK,CAACO,IAAI,CAACL,MAAM,CAAC;UAClB;QACF;QAEA,IAAIQ,SAAS,GAAG,CAACR,MAAM;;QAEvB;QACA,IAAIM,MAAM,CAACC,KAAK,CAACC,SAAS,CAAC,IAAIA,SAAS,GAAG5D,WAAW,CAACC,GAAG,IAAI2D,SAAS,GAAG5D,WAAW,CAACE,GAAG,EAAE;UACzF,MAAM,IAAI4C,KAAK,CACb,8BAA8B,GAAGM,MAAM,GAAG,kBAAkB,GAC5DpD,WAAW,CAACC,GAAG,GAAG,GAAG,GAAGD,WAAW,CAACE,GACtC,CAAC;QACH;QAEA,IAAIwC,KAAK,KAAK,WAAW,EAAE;UACzBkB,SAAS,GAAGA,SAAS,GAAG,CAAC;QAC3B;QAEAV,KAAK,CAACO,IAAI,CAACG,SAAS,CAAC;MACvB;IACF;IAEA,IAAIC,KAAK,GAAGZ,GAAG,CAACa,KAAK,CAAC,GAAG,CAAC;IAC1B,IAAI,CAACD,KAAK,CAACE,KAAK,CAAC,UAAUC,IAAI,EAAE;MAC/B,OAAOA,IAAI,CAACR,MAAM,GAAG,CAAC;IACxB,CAAC,CAAC,EAAE;MACF,MAAM,IAAIV,KAAK,CAAC,2BAA2B,CAAC;IAC9C;IAEA,IAAIe,KAAK,CAACL,MAAM,GAAG,CAAC,EAAE;MACpB,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGM,KAAK,CAACL,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;QAC5CH,YAAY,CAACc,WAAW,CAACJ,KAAK,CAACP,CAAC,CAAC,CAAC,CAAC;MACrC;IACF,CAAC,MAAM;MACLH,YAAY,CAACc,WAAW,CAAChB,GAAG,CAAC,CAAC;IAChC;IAEAC,KAAK,CAACgB,IAAI,CAACxF,cAAc,CAACyF,cAAc,CAAC;IAEzC,OAAOjB,KAAK;EACd;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAASe,WAAWA,CAAEhB,GAAG,EAAE;IACzB,IAAImB,cAAc,GAAG,CAAC;IACtB,IAAIP,KAAK,GAAGZ,GAAG,CAACa,KAAK,CAAC,GAAG,CAAC;IAE1B,IAAID,KAAK,CAACL,MAAM,GAAG,CAAC,EAAE;MACpB,MAAM,IAAIV,KAAK,CAAC,kBAAkB,GAAGG,GAAG,CAAC;IAC3C;IAEA,IAAIY,KAAK,CAACL,MAAM,GAAG,CAAC,EAAE;MACpB,IAAIK,KAAK,CAAC,CAAC,CAAC,IAAI,CAACA,KAAK,CAAC,CAAC,CAAC,EAAE;QACzBA,KAAK,GAAG,CAACA,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG7D,WAAW,CAACE,GAAG,EAAE2D,KAAK,CAAC,CAAC,CAAC,CAAC;MACtD;MACA,OAAOQ,UAAU,CAACR,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAACA,KAAK,CAACL,MAAM,GAAG,CAAC,CAAC,CAAC;IACtD;IAEA,OAAOa,UAAU,CAACpB,GAAG,EAAEmB,cAAc,CAAC;EACxC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,UAAUA,CAAEpB,GAAG,EAAEmB,cAAc,EAAE;IACxC,IAAIlB,KAAK,GAAG,EAAE;IACd,IAAIW,KAAK,GAAGZ,GAAG,CAACa,KAAK,CAAC,GAAG,CAAC;IAE1B,IAAID,KAAK,CAACL,MAAM,GAAG,CAAC,EAAG;MACrB;MACA,IAAIK,KAAK,CAACL,MAAM,GAAG,CAAC,EAAE;QACpB,OAAO,CAACP,GAAG;MACb;MAEA,IAAI,CAACY,KAAK,CAAC,CAAC,CAAC,CAACL,MAAM,EAAE;QACpB,IAAI,CAACK,KAAK,CAAC,CAAC,CAAC,CAACL,MAAM,EAAE;UACpB,MAAM,IAAIV,KAAK,CAAC,iBAAiB,GAAGG,GAAG,CAAC;QAC1C;QAEA,OAAO,CAACA,GAAG;MACb;;MAEA;MACA,IAAIhD,GAAG,GAAG,CAAC4D,KAAK,CAAC,CAAC,CAAC;MACnB,IAAI3D,GAAG,GAAG,CAAC2D,KAAK,CAAC,CAAC,CAAC;MAEnB,IAAIH,MAAM,CAACC,KAAK,CAAC1D,GAAG,CAAC,IAAIyD,MAAM,CAACC,KAAK,CAACzD,GAAG,CAAC,IACtCD,GAAG,GAAGD,WAAW,CAACC,GAAG,IAAIC,GAAG,GAAGF,WAAW,CAACE,GAAG,EAAE;QAClD,MAAM,IAAI4C,KAAK,CACb,8BAA8B,GAC9B7C,GAAG,GAAG,GAAG,GAAGC,GAAG,GACf,kBAAkB,GAClBF,WAAW,CAACC,GAAG,GAAG,GAAG,GAAGD,WAAW,CAACE,GACtC,CAAC;MACH,CAAC,MAAM,IAAID,GAAG,GAAGC,GAAG,EAAE;QACpB,MAAM,IAAI4C,KAAK,CAAC,iBAAiB,GAAGG,GAAG,CAAC;MAC1C;;MAEA;MACA,IAAIqB,WAAW,GAAG,CAACF,cAAc;MAEjC,IAAIV,MAAM,CAACC,KAAK,CAACW,WAAW,CAAC,IAAIA,WAAW,IAAI,CAAC,EAAE;QACjD,MAAM,IAAIxB,KAAK,CAAC,2CAA2C,GAAGwB,WAAW,GAAG,QAAQ,CAAC;MACvF;;MAEA;MACA;MACA,IAAI5B,KAAK,KAAK,WAAW,IAAIxC,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;QAC1CgD,KAAK,CAACO,IAAI,CAAC,CAAC,CAAC;MACf;MAEA,KAAK,IAAIc,KAAK,GAAGtE,GAAG,EAAEuE,KAAK,GAAGtE,GAAG,EAAEqE,KAAK,IAAIC,KAAK,EAAED,KAAK,EAAE,EAAE;QAC1D,IAAIE,MAAM,GAAGvB,KAAK,CAACV,OAAO,CAAC+B,KAAK,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,CAACE,MAAM,IAAIH,WAAW,GAAG,CAAC,IAAKA,WAAW,GAAGF,cAAc,KAAM,CAAC,EAAE;UACtEE,WAAW,GAAG,CAAC;UACfpB,KAAK,CAACO,IAAI,CAACc,KAAK,CAAC;QACnB,CAAC,MAAM;UACLD,WAAW,EAAE;QACf;MACF;MACA,OAAOpB,KAAK;IACd;IAEA,OAAOQ,MAAM,CAACC,KAAK,CAAC,CAACV,GAAG,CAAC,GAAGA,GAAG,GAAG,CAACA,GAAG;EACxC;EAEA,OAAOD,aAAa,CAACX,KAAK,CAAC;AAC7B,CAAC;AAED3D,cAAc,CAACyF,cAAc,GAAG,UAASO,CAAC,EAAEC,CAAC,EAAE;EAC7C,IAAIC,SAAS,GAAG,OAAOF,CAAC,KAAK,QAAQ;EACrC,IAAIG,SAAS,GAAG,OAAOF,CAAC,KAAK,QAAQ;EAErC,IAAIC,SAAS,IAAIC,SAAS,EAAE;IAC1B,OAAOH,CAAC,GAAGC,CAAC;EACd;EAEA,IAAI,CAACC,SAAS,IAAIC,SAAS,EAAE;IAC3B,OAAO,CAAC;EACV;EAEA,IAAID,SAAS,IAAI,CAACC,SAAS,EAAE;IAC3B,OAAO,CAAC,CAAC;EACX;EAEA,OAAOH,CAAC,CAACI,aAAa,CAACH,CAAC,CAAC;AAC3B,CAAC;AAEDjG,cAAc,CAACqG,qBAAqB,GAAG,UAASC,YAAY,EAAE;EAC5D;EACA,IAAIA,YAAY,CAAC1E,KAAK,CAACkD,MAAM,KAAK,CAAC,EAAE;IACnC,IAAIpD,WAAW,GAAG1B,cAAc,CAAC0B,WAAW,CAAC4E,YAAY,CAAC1E,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAEvE,IAAI0E,YAAY,CAAC7C,UAAU,CAAC,CAAC,CAAC,GAAG/B,WAAW,EAAE;MAC5C,MAAM,IAAI0C,KAAK,CAAC,0CAA0C,CAAC;IAC7D;IAEA,OAAOkC,YAAY,CAAC7C,UAAU,CAC3B8C,MAAM,CAAC,UAAS9C,UAAU,EAAE;MAC3B,OAAOA,UAAU,KAAK,GAAG,GAAG,IAAI,GAAGA,UAAU,IAAI/B,WAAW;IAC9D,CAAC,CAAC,CACD8D,IAAI,CAACxF,cAAc,CAACyF,cAAc,CAAC;EACxC;AACF,CAAC;AAEDzF,cAAc,CAACmB,aAAa,GAAG,UAASlB,MAAM,EAAE;EAC9C,KAAK,IAAI2E,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG7E,cAAc,CAACoB,GAAG,CAAC0D,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;IACzD,IAAIZ,KAAK,GAAGhE,cAAc,CAACoB,GAAG,CAACwD,CAAC,CAAC,CAAC,CAAC;IACnC,IAAIjB,KAAK,GAAG1D,MAAM,CAAC+D,KAAK,CAAC;IACzB/D,MAAM,CAAC+D,KAAK,CAAC,GAAGwC,MAAM,CAACC,MAAM,CAAC9C,KAAK,CAAC;EACtC;EACA,OAAO6C,MAAM,CAACC,MAAM,CAACxG,MAAM,CAAC;AAC9B,CAAC;AAEDD,cAAc,CAAC0G,SAAS,CAACC,mBAAmB,GAAG,UAASlG,WAAW,EAAEmG,YAAY,EAAEC,MAAM,EAAE;EACzF,IAAKA,MAAM,KAAK,OAAO,IAAMA,MAAM,KAAK,KAAM,EAAE;IAC9C,IAAIC,QAAQ,GAAGrG,WAAW,CAACsG,OAAO,CAAC,CAAC;IACpCtG,WAAW,CAACmG,YAAY,GAAGC,MAAM,CAAC,CAAC,CAAC;IACpC,IAAIG,QAAQ,GAAGvG,WAAW,CAACsG,OAAO,CAAC,CAAC;IACpC,IAAID,QAAQ,KAAKE,QAAQ,EAAE;MACzB;MACA,IAAKvG,WAAW,CAACwG,UAAU,CAAC,CAAC,KAAK,CAAC,IAC9BxG,WAAW,CAACyG,UAAU,CAAC,CAAC,KAAK,CAAE,EAAE;QACpCzG,WAAW,CAAC0G,OAAO,CAAC,CAAC;MACvB,CAAC,MAAM,IAAK1G,WAAW,CAACwG,UAAU,CAAC,CAAC,KAAK,EAAE,IAC/BxG,WAAW,CAACyG,UAAU,CAAC,CAAC,KAAK,EAAG,EAAE;QAC5CzG,WAAW,CAAC2G,YAAY,CAAC,CAAC;MAC5B;IACF;EACF,CAAC,MAAM;IACL,IAAIC,YAAY,GAAG5G,WAAW,CAAC6G,QAAQ,CAAC,CAAC;IACzC7G,WAAW,CAACmG,YAAY,GAAGC,MAAM,CAAC,CAAC,CAAC;IACpC,IAAIU,WAAW,GAAG9G,WAAW,CAAC6G,QAAQ,CAAC,CAAC;IACxC,IAAIE,IAAI,GAAGD,WAAW,GAAGF,YAAY;IACrC,IAAIG,IAAI,KAAK,CAAC,EAAE;MACZ;MACA,IAAI,IAAI,CAACvH,MAAM,CAACuD,IAAI,CAACsB,MAAM,KAAK,EAAE,EAAE;QAClC;QACA,IAAI,CAAC2C,SAAS,GAAGF,WAAW;MAC9B;IACF,CAAC,MAAM,IAAKC,IAAI,KAAK,CAAC,IACV/G,WAAW,CAACwG,UAAU,CAAC,CAAC,KAAK,CAAE,IAC/BxG,WAAW,CAACyG,UAAU,CAAC,CAAC,KAAK,CAAE,EAAE;MAC3C;MACA,IAAI,IAAI,CAACjH,MAAM,CAACuD,IAAI,CAACsB,MAAM,KAAK,EAAE,EAAE;QAClC;QACA,IAAI,CAAC4C,OAAO,GAAGH,WAAW;MAC5B;IACF;EACJ;AACF,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACAvH,cAAc,CAAC0G,SAAS,CAACiB,aAAa,GAAG,SAASA,aAAaA,CAAEC,OAAO,EAAE;EAExE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,aAAaA,CAAElE,KAAK,EAAEmE,QAAQ,EAAE;IACvC,KAAK,IAAIlD,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGiD,QAAQ,CAAChD,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAC/C,IAAIkD,QAAQ,CAAClD,CAAC,CAAC,IAAIjB,KAAK,EAAE;QACxB,OAAOmE,QAAQ,CAAClD,CAAC,CAAC,KAAKjB,KAAK;MAC9B;IACF;IAEA,OAAOmE,QAAQ,CAAC,CAAC,CAAC,KAAKnE,KAAK;EAC9B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASoE,aAAaA,CAACC,IAAI,EAAE9G,YAAY,EAAE;IACzC,IAAIA,YAAY,GAAG,CAAC,EAAE;MACpB,IACE8G,IAAI,CAACC,OAAO,CAAC,CAAC,GAAG,CAAC,IAClB/G,YAAY,KAAK,CAAC,CAAC;MAAA,EACnB;QACA,OAAO,IAAI;MACb;MAEA,IAAIgH,MAAM,GAAGF,IAAI,CAACC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MACzC,IAAIE,YAAY,GAAGH,IAAI,CAACC,OAAO,CAAC,CAAC,GAAID,IAAI,CAACC,OAAO,CAAC,CAAC,GAAG,CAAE,CAAC,CAAC;MAC1D,IAAIG,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,GAAG,CAAC,CAAC,GAAGD,MAAM;MAEtD,OAAOE,UAAU,KAAKlH,YAAY;IACpC;IAEA,OAAO,KAAK;EACd;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASqH,gBAAgBA,CAACC,WAAW,EAAE;IACrC,OAAOA,WAAW,CAAC1D,MAAM,GAAG,CAAC,IAAI0D,WAAW,CAAC5E,IAAI,CAAC,UAAS6E,UAAU,EAAE;MACrE,OAAO,OAAOA,UAAU,KAAK,QAAQ,IAAIA,UAAU,CAAC3E,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC;IACvE,CAAC,CAAC;EACJ;;EAGA;EACA8D,OAAO,GAAGA,OAAO,IAAI,KAAK;EAC1B,IAAIhB,YAAY,GAAGgB,OAAO,GAAG,UAAU,GAAG,KAAK;EAE/C,IAAInH,WAAW,GAAG,IAAIb,QAAQ,CAAC,IAAI,CAACY,YAAY,EAAE,IAAI,CAACF,GAAG,CAAC;EAC3D,IAAIK,SAAS,GAAG,IAAI,CAACD,UAAU;EAC/B,IAAIG,OAAO,GAAG,IAAI,CAACD,QAAQ;;EAE3B;EACA,IAAI8H,cAAc,GAAGjI,WAAW,CAACsG,OAAO,CAAC,CAAC;EAC1C,IAAI4B,SAAS,GAAG,CAAC;EAEjB,SAASC,yBAAyBA,CAACJ,WAAW,EAAE;IAC9C,OAAOA,WAAW,CAAC5E,IAAI,CAAC,UAAS6E,UAAU,EAAE;MAC3C;MACA;MACA,IAAI,CAACF,gBAAgB,CAAC,CAACE,UAAU,CAAC,CAAC,EAAE;QACnC,OAAO,KAAK;MACd;;MAEA;MACA,IAAII,OAAO,GAAG7D,MAAM,CAAC8D,QAAQ,CAACL,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;MAEhD,IAAIzD,MAAM,CAACC,KAAK,CAAC4D,OAAO,CAAC,EAAE;QACzB,MAAM,IAAIzE,KAAK,CAAC,gDAAgD,GAAGqE,UAAU,CAAC;MAChF;MAEA,OAAOhI,WAAW,CAACsI,MAAM,CAAC,CAAC,KAAKF,OAAO,IAAIpI,WAAW,CAACuI,oBAAoB,CAAC,CAAC;IAC/E,CAAC,CAAC;EACJ;EAEA,OAAOL,SAAS,GAAG5I,UAAU,EAAE;IAC7B4I,SAAS,EAAE;;IAEX;IACA,IAAIf,OAAO,EAAE;MACX,IAAIjH,SAAS,IAAKF,WAAW,CAACsG,OAAO,CAAC,CAAC,GAAGpG,SAAS,CAACoG,OAAO,CAAC,CAAC,GAAG,CAAE,EAAE;QAClE,MAAM,IAAI3C,KAAK,CAAC,2BAA2B,CAAC;MAC9C;IACF,CAAC,MAAM;MACL,IAAIvD,OAAO,IAAKA,OAAO,CAACkG,OAAO,CAAC,CAAC,GAAGtG,WAAW,CAACsG,OAAO,CAAC,CAAC,GAAI,CAAC,EAAE;QAC9D,MAAM,IAAI3C,KAAK,CAAC,2BAA2B,CAAC;MAC9C;IACF;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA,IAAI6E,eAAe,GAAGpB,aAAa,CAACpH,WAAW,CAACwH,OAAO,CAAC,CAAC,EAAE,IAAI,CAAChI,MAAM,CAACwD,UAAU,CAAC;IAClF,IAAI8E,gBAAgB,CAAC,IAAI,CAACtI,MAAM,CAACwD,UAAU,CAAC,EAAE;MAC5CwF,eAAe,GAAGA,eAAe,IAAIxI,WAAW,CAACyI,gBAAgB,CAAC,CAAC;IACrE;IACA,IAAIC,cAAc,GAAGtB,aAAa,CAACpH,WAAW,CAACsI,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC9I,MAAM,CAACwC,SAAS,CAAC;IAC/E,IAAI8F,gBAAgB,CAAC,IAAI,CAACtI,MAAM,CAACwC,SAAS,CAAC,EAAE;MAC3C0G,cAAc,GAAGA,cAAc,IAAIP,yBAAyB,CAAC,IAAI,CAAC3I,MAAM,CAACwC,SAAS,CAAC;IACrF;IACA,IAAI2G,yBAAyB,GAAG,IAAI,CAACnJ,MAAM,CAACwD,UAAU,CAACqB,MAAM,IAAI9E,cAAc,CAAC0B,WAAW,CAACjB,WAAW,CAAC4I,QAAQ,CAAC,CAAC,CAAC;IACnH,IAAIC,wBAAwB,GAAG,IAAI,CAACrJ,MAAM,CAACwC,SAAS,CAACqC,MAAM,KAAK9E,cAAc,CAACsB,WAAW,CAAC,CAAC,CAAC,CAACE,GAAG,GAAGxB,cAAc,CAACsB,WAAW,CAAC,CAAC,CAAC,CAACC,GAAG,GAAG,CAAC;IACzI,IAAIgG,WAAW,GAAG9G,WAAW,CAAC6G,QAAQ,CAAC,CAAC;;IAExC;IACA,IAAI,CAAC2B,eAAe,KAAK,CAACE,cAAc,IAAIG,wBAAwB,CAAC,EAAE;MACrE,IAAI,CAAC3C,mBAAmB,CAAClG,WAAW,EAAEmG,YAAY,EAAE,KAAK,CAAC;MAC1D;IACF;;IAEA;IACA,IAAI,CAACwC,yBAAyB,IAAIE,wBAAwB,IAAI,CAACL,eAAe,EAAE;MAC9E,IAAI,CAACtC,mBAAmB,CAAClG,WAAW,EAAEmG,YAAY,EAAE,KAAK,CAAC;MAC1D;IACF;;IAEA;IACA,IAAIwC,yBAAyB,IAAI,CAACE,wBAAwB,IAAI,CAACH,cAAc,EAAE;MAC7E,IAAI,CAACxC,mBAAmB,CAAClG,WAAW,EAAEmG,YAAY,EAAE,KAAK,CAAC;MAC1D;IACF;;IAEA;IACA,IACE,IAAI,CAAC3F,aAAa,GAAG,CAAC,IACtB,CAAC8G,aAAa,CAACtH,WAAW,EAAE,IAAI,CAACQ,aAAa,CAAC,EAC/C;MACA,IAAI,CAAC0F,mBAAmB,CAAClG,WAAW,EAAEmG,YAAY,EAAE,KAAK,CAAC;MAC1D;IACF;;IAEA;IACA,IAAI,CAACiB,aAAa,CAACpH,WAAW,CAAC4I,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAACpJ,MAAM,CAAC2B,KAAK,CAAC,EAAE;MACjE,IAAI,CAAC+E,mBAAmB,CAAClG,WAAW,EAAEmG,YAAY,EAAE,OAAO,CAAC;MAC5D;IACF;;IAEA;IACA,IAAI,CAACiB,aAAa,CAACN,WAAW,EAAE,IAAI,CAACtH,MAAM,CAACuD,IAAI,CAAC,EAAE;MACjD,IAAI,IAAI,CAACiE,SAAS,KAAKF,WAAW,EAAE;QAClC,IAAI,CAACE,SAAS,GAAG,IAAI;QACrB,IAAI,CAACd,mBAAmB,CAAClG,WAAW,EAAEmG,YAAY,EAAE,MAAM,CAAC;QAC3D;MACF,CAAC,MAAM,IAAI,CAACiB,aAAa,CAACN,WAAW,GAAG,CAAC,EAAE,IAAI,CAACtH,MAAM,CAACuD,IAAI,CAAC,EAAE;QAC5D/C,WAAW,CAACmG,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC;QACpC;MACF;IACF,CAAC,MAAM,IAAI,IAAI,CAACc,OAAO,KAAKH,WAAW,EAAE;MACvC,IAAI,CAACK,OAAO,EAAE;QACZ,IAAI,CAACF,OAAO,GAAG,IAAI;QACnB,IAAI,CAACf,mBAAmB,CAAClG,WAAW,EAAE,KAAK,EAAE,MAAM,CAAC;QACpD;MACF;IACF;;IAEA;IACA,IAAI,CAACoH,aAAa,CAACpH,WAAW,CAACwG,UAAU,CAAC,CAAC,EAAE,IAAI,CAAChH,MAAM,CAACsD,MAAM,CAAC,EAAE;MAChE,IAAI,CAACoD,mBAAmB,CAAClG,WAAW,EAAEmG,YAAY,EAAE,QAAQ,CAAC;MAC7D;IACF;;IAEA;IACA,IAAI,CAACiB,aAAa,CAACpH,WAAW,CAACyG,UAAU,CAAC,CAAC,EAAE,IAAI,CAACjH,MAAM,CAACqD,MAAM,CAAC,EAAE;MAChE,IAAI,CAACqD,mBAAmB,CAAClG,WAAW,EAAEmG,YAAY,EAAE,QAAQ,CAAC;MAC7D;IACF;;IAEA;IACA;IACA,IAAI8B,cAAc,KAAKjI,WAAW,CAACsG,OAAO,CAAC,CAAC,EAAE;MAC5C,IAAKH,YAAY,KAAK,KAAK,IAAMnG,WAAW,CAAC8I,eAAe,CAAC,CAAC,KAAK,CAAE,EAAE;QACrE,IAAI,CAAC5C,mBAAmB,CAAClG,WAAW,EAAEmG,YAAY,EAAE,QAAQ,CAAC;MAC/D,CAAC,MAAM;QACLnG,WAAW,CAAC+I,eAAe,CAAC,CAAC,CAAC;MAChC;MAEA;IACF;IAEA;EACF;EAEA,IAAIb,SAAS,IAAI5I,UAAU,EAAE;IAC3B,MAAM,IAAIqE,KAAK,CAAC,yCAAyC,CAAC;EAC5D;EAEA,IAAI,CAAC5D,YAAY,GAAG,IAAIZ,QAAQ,CAACa,WAAW,EAAE,IAAI,CAACH,GAAG,CAAC;EACvD,IAAI,CAACU,YAAY,GAAG,IAAI;EAExB,OAAOP,WAAW;AACpB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACAT,cAAc,CAAC0G,SAAS,CAAC+C,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAI;EAC/C,IAAIC,QAAQ,GAAG,IAAI,CAAC/B,aAAa,CAAC,CAAC;;EAEnC;EACA,IAAI,IAAI,CAAC7G,WAAW,EAAE;IACpB,OAAO;MACL6C,KAAK,EAAE+F,QAAQ;MACfC,IAAI,EAAE,CAAC,IAAI,CAACC,OAAO,CAAC;IACtB,CAAC;EACH;EAEA,OAAOF,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA1J,cAAc,CAAC0G,SAAS,CAACmD,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAI;EAC/C,IAAIH,QAAQ,GAAG,IAAI,CAAC/B,aAAa,CAAC,IAAI,CAAC;;EAEvC;EACA,IAAI,IAAI,CAAC7G,WAAW,EAAE;IACpB,OAAO;MACL6C,KAAK,EAAE+F,QAAQ;MACfC,IAAI,EAAE,CAAC,IAAI,CAACG,OAAO,CAAC;IACtB,CAAC;EACH;EAEA,OAAOJ,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA1J,cAAc,CAAC0G,SAAS,CAACkD,OAAO,GAAG,YAAW;EAC5C,IAAIG,OAAO,GAAG,IAAI,CAACvJ,YAAY;EAC/B,IAAIwJ,WAAW,GAAG,IAAI,CAAChJ,YAAY;EAEnC,IAAI;IACF,IAAI,CAAC2G,aAAa,CAAC,CAAC;IACpB,OAAO,IAAI;EACb,CAAC,CAAC,OAAOsC,GAAG,EAAE;IACZ,OAAO,KAAK;EACd,CAAC,SAAS;IACR,IAAI,CAACzJ,YAAY,GAAGuJ,OAAO;IAC3B,IAAI,CAAC/I,YAAY,GAAGgJ,WAAW;EACjC;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACAhK,cAAc,CAAC0G,SAAS,CAACoD,OAAO,GAAG,YAAW;EAC5C,IAAIC,OAAO,GAAG,IAAI,CAACvJ,YAAY;EAC/B,IAAIwJ,WAAW,GAAG,IAAI,CAAChJ,YAAY;EAEnC,IAAI;IACF,IAAI,CAAC2G,aAAa,CAAC,IAAI,CAAC;IACxB,OAAO,IAAI;EACb,CAAC,CAAC,OAAOsC,GAAG,EAAE;IACZ,OAAO,KAAK;EACd,CAAC,SAAS;IACR,IAAI,CAACzJ,YAAY,GAAGuJ,OAAO;IAC3B,IAAI,CAAC/I,YAAY,GAAGgJ,WAAW;EACjC;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAhK,cAAc,CAAC0G,SAAS,CAACwD,OAAO,GAAG,SAASA,OAAOA,CAAEC,KAAK,EAAEC,QAAQ,EAAE;EACpE,IAAIC,KAAK,GAAG,EAAE;EAEd,IAAIF,KAAK,IAAI,CAAC,EAAE;IACd,KAAK,IAAIvF,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGsF,KAAK,EAAEvF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACrC,IAAI;QACF,IAAI0F,IAAI,GAAG,IAAI,CAACb,IAAI,CAAC,CAAC;QACtBY,KAAK,CAACtF,IAAI,CAACuF,IAAI,CAAC;;QAEhB;QACA,IAAIF,QAAQ,EAAE;UACZA,QAAQ,CAACE,IAAI,EAAE1F,CAAC,CAAC;QACnB;MACF,CAAC,CAAC,OAAOqF,GAAG,EAAE;QACZ;MACF;IACF;EACF,CAAC,MAAM;IACL,KAAK,IAAIrF,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGsF,KAAK,EAAEvF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACrC,IAAI;QACF,IAAI0F,IAAI,GAAG,IAAI,CAACT,IAAI,CAAC,CAAC;QACtBQ,KAAK,CAACtF,IAAI,CAACuF,IAAI,CAAC;;QAEhB;QACA,IAAIF,QAAQ,EAAE;UACZA,QAAQ,CAACE,IAAI,EAAE1F,CAAC,CAAC;QACnB;MACF,CAAC,CAAC,OAAOqF,GAAG,EAAE;QACZ;MACF;IACF;EACF;EAEA,OAAOI,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACArK,cAAc,CAAC0G,SAAS,CAAC6D,KAAK,GAAG,SAASA,KAAKA,CAAEC,OAAO,EAAE;EACxD,IAAI,CAAChK,YAAY,GAAG,IAAIZ,QAAQ,CAAC4K,OAAO,IAAI,IAAI,CAACrK,QAAQ,CAACM,WAAW,CAAC;AACxE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACAT,cAAc,CAAC0G,SAAS,CAAC+D,SAAS,GAAG,SAASA,SAASA,CAACC,cAAc,EAAE;EACtE,IAAIC,SAAS,GAAG,EAAE;EAClB,KAAK,IAAI/F,CAAC,GAAG8F,cAAc,GAAG,CAAC,GAAG,CAAC,EAAE7F,CAAC,GAAG7E,cAAc,CAACoB,GAAG,CAAC0D,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;IAC9E,IAAIZ,KAAK,GAAGhE,cAAc,CAACoB,GAAG,CAACwD,CAAC,CAAC;IACjC,IAAIjB,KAAK,GAAG,IAAI,CAAC1D,MAAM,CAAC+D,KAAK,CAAC;IAC9B,IAAI4G,UAAU,GAAG5K,cAAc,CAACsB,WAAW,CAACsD,CAAC,CAAC;IAE9C,IAAIZ,KAAK,KAAK,YAAY,IAAI,IAAI,CAAC/D,MAAM,CAAC2B,KAAK,CAACkD,MAAM,KAAK,CAAC,EAAE;MAC5D8F,UAAU,GAAG;QAAErJ,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAExB,cAAc,CAAC0B,WAAW,CAAC,IAAI,CAACzB,MAAM,CAAC2B,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;MAAE,CAAC;IACpF,CAAC,MAAM,IAAIoC,KAAK,KAAK,WAAW,EAAE;MAChC;MACA4G,UAAU,GAAG;QAAErJ,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE,CAAC;MAC/BmC,KAAK,GAAGA,KAAK,CAACA,KAAK,CAACmB,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,GAAGnB,KAAK,CAACkH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGlH,KAAK;IACpE;IAEAgH,SAAS,CAAC5F,IAAI,CAACjF,cAAc,CAAC6D,KAAK,EAAEiH,UAAU,CAACrJ,GAAG,EAAEqJ,UAAU,CAACpJ,GAAG,CAAC,CAAC;EACvE;EACA,OAAOmJ,SAAS,CAACG,IAAI,CAAC,GAAG,CAAC;AAC5B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA9K,cAAc,CAAC+K,KAAK,GAAG,SAASA,KAAKA,CAACtC,UAAU,EAAEvI,OAAO,EAAE;EACzD,IAAI8K,IAAI,GAAG,IAAI;EACf,IAAI,OAAO9K,OAAO,KAAK,UAAU,EAAE;IACjCA,OAAO,GAAG,CAAC,CAAC;EACd;EAEA,SAAS6K,KAAKA,CAAEtC,UAAU,EAAEvI,OAAO,EAAE;IACnC,IAAI,CAACA,OAAO,EAAE;MACZA,OAAO,GAAG,CAAC,CAAC;IACd;IAEA,IAAI,OAAOA,OAAO,CAACO,WAAW,KAAK,WAAW,EAAE;MAC9CP,OAAO,CAACO,WAAW,GAAG,IAAIb,QAAQ,CAACqL,SAAS,EAAED,IAAI,CAAC1K,GAAG,CAAC;IACzD;;IAEA;IACA,IAAIN,cAAc,CAACqB,UAAU,CAACoH,UAAU,CAAC,EAAE;MACzCA,UAAU,GAAGzI,cAAc,CAACqB,UAAU,CAACoH,UAAU,CAAC;IACpD;;IAEA;IACA,IAAIxI,MAAM,GAAG,EAAE;IACf,IAAIkF,KAAK,GAAG,CAACsD,UAAU,GAAG,EAAE,EAAEyC,IAAI,CAAC,CAAC,CAAC9F,KAAK,CAAC,KAAK,CAAC;IAEjD,IAAID,KAAK,CAACL,MAAM,GAAG,CAAC,EAAE;MACpB,MAAM,IAAIV,KAAK,CAAC,yBAAyB,CAAC;IAC5C;;IAEA;IACA,IAAI+G,KAAK,GAAInL,cAAc,CAACoB,GAAG,CAAC0D,MAAM,GAAGK,KAAK,CAACL,MAAO;IACtD,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG7E,cAAc,CAACoB,GAAG,CAAC0D,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MACzD,IAAIZ,KAAK,GAAGhE,cAAc,CAACoB,GAAG,CAACwD,CAAC,CAAC,CAAC,CAAC;MACnC,IAAIjB,KAAK,GAAGwB,KAAK,CAACA,KAAK,CAACL,MAAM,GAAGD,CAAC,GAAGD,CAAC,GAAGA,CAAC,GAAGuG,KAAK,CAAC,CAAC,CAAC;;MAErD,IAAIvG,CAAC,GAAGuG,KAAK,IAAI,CAACxH,KAAK,EAAE;QAAE;QACzB1D,MAAM,CAAC8E,IAAI,CAAC/E,cAAc,CAAC+D,WAAW,CACpCC,KAAK,EACLhE,cAAc,CAACiD,aAAa,CAAC2B,CAAC,CAAC,EAC/B5E,cAAc,CAACsB,WAAW,CAACsD,CAAC,CAC5B,CACF,CAAC;MACH,CAAC,MAAM;QACL,IAAIL,GAAG,GAAGP,KAAK,KAAK,WAAW,GAAGoH,WAAW,CAACzH,KAAK,CAAC,GAAGA,KAAK;QAE5D1D,MAAM,CAAC8E,IAAI,CAAC/E,cAAc,CAAC+D,WAAW,CACpCC,KAAK,EACLO,GAAG,EACHvE,cAAc,CAACsB,WAAW,CAACsD,CAAC,CAC5B,CACF,CAAC;MACH;IACF;IAEA,IAAI0B,YAAY,GAAG,CAAC,CAAC;IACrB,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG7E,cAAc,CAACoB,GAAG,CAAC0D,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACzD,IAAIyG,GAAG,GAAGrL,cAAc,CAACoB,GAAG,CAACwD,CAAC,CAAC;MAC/B0B,YAAY,CAAC+E,GAAG,CAAC,GAAGpL,MAAM,CAAC2E,CAAC,CAAC;IAC/B;IAEA,IAAInB,UAAU,GAAGzD,cAAc,CAACqG,qBAAqB,CAACC,YAAY,CAAC;IACnEA,YAAY,CAAC7C,UAAU,GAAGA,UAAU,IAAI6C,YAAY,CAAC7C,UAAU;IAC/D,OAAO,IAAIzD,cAAc,CAACsG,YAAY,EAAEpG,OAAO,CAAC;;IAEhD;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,SAASkL,WAAWA,CAAC7G,GAAG,EAAE;MACxB,IAAIY,KAAK,GAAGZ,GAAG,CAACa,KAAK,CAAC,GAAG,CAAC;MAC1B,IAAID,KAAK,CAACL,MAAM,GAAG,CAAC,EAAE;QACpB,IAAIwG,QAAQ,GAAG,CAACnG,KAAK,CAACA,KAAK,CAACL,MAAM,GAAG,CAAC,CAAC;QACvC,IAAG,GAAG,CAACT,IAAI,CAACE,GAAG,CAAC,EAAE;UAChB,MAAM,IAAIH,KAAK,CAAC,kDAAkD,GAC9D,qCAAqC,CAAC;QAC5C;QACA,IAAG,IAAI,CAACC,IAAI,CAACE,GAAG,CAAC,EAAE;UACjB,MAAM,IAAIH,KAAK,CAAC,kDAAkD,GAC9D,qCAAqC,CAAC;QAC5C;QACA,IAAG,GAAG,CAACC,IAAI,CAACE,GAAG,CAAC,EAAE;UAChB,MAAM,IAAIH,KAAK,CAAC,kDAAkD,GAC9D,qCAAqC,CAAC;QAC5C;QACA,IAAIe,KAAK,CAACL,MAAM,GAAG,CAAC,IAAIE,MAAM,CAACC,KAAK,CAACqG,QAAQ,CAAC,IAAKA,QAAQ,GAAG,CAAC,IAAIA,QAAQ,GAAG,CAAE,EAAE;UAChF,MAAM,IAAIlH,KAAK,CAAC,2DAA2D,CAAC;QAC9E;QAEAlE,OAAO,CAACgB,YAAY,GAAGoK,QAAQ;QAC/B,OAAOnG,KAAK,CAAC,CAAC,CAAC;MACjB;MACA,OAAOZ,GAAG;IACZ;EACF;EAEA,OAAOwG,KAAK,CAACtC,UAAU,EAAEvI,OAAO,CAAC;AACnC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAF,cAAc,CAACuL,kBAAkB,GAAG,SAASA,kBAAkBA,CAACtL,MAAM,EAAEC,OAAO,EAAE;EAC/E,SAASsL,mBAAmBA,CAAExH,KAAK,EAAEyH,MAAM,EAAEnK,WAAW,EAAE;IACxD,IAAI,CAACmK,MAAM,EAAE;MACX,MAAM,IAAIrH,KAAK,CAAC,0BAA0B,GAAGJ,KAAK,GAAG,aAAa,CAAC;IACrE;IACA,IAAIyH,MAAM,CAAC3G,MAAM,KAAK,CAAC,EAAE;MACvB,MAAM,IAAIV,KAAK,CAAC,0BAA0B,GAAGJ,KAAK,GAAG,qBAAqB,CAAC;IAC7E;IACA,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG4G,MAAM,CAAC3G,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAC7C,IAAIjB,KAAK,GAAG8H,MAAM,CAAC7G,CAAC,CAAC;MAErB,IAAI5E,cAAc,CAAC0D,sBAAsB,CAACpC,WAAW,EAAEqC,KAAK,CAAC,EAAE;QAC7D;MACF;;MAEA;MACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIqB,MAAM,CAACC,KAAK,CAACtB,KAAK,CAAC,IAAIA,KAAK,GAAGrC,WAAW,CAACC,GAAG,IAAIoC,KAAK,GAAGrC,WAAW,CAACE,GAAG,EAAE;QAC1G,MAAM,IAAI4C,KAAK,CACb,8BAA8B,GAAGT,KAAK,GAAG,kBAAkB,GAC3DrC,WAAW,CAACC,GAAG,GAAG,GAAG,GAAGD,WAAW,CAACE,GACtC,CAAC;MACH;IACF;EACF;EAEA,IAAI8E,YAAY,GAAG,CAAC,CAAC;EACrB,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG7E,cAAc,CAACoB,GAAG,CAAC0D,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;IACzD,IAAIZ,KAAK,GAAGhE,cAAc,CAACoB,GAAG,CAACwD,CAAC,CAAC,CAAC,CAAC;IACnC,IAAI6G,MAAM,GAAGxL,MAAM,CAAC+D,KAAK,CAAC;IAC1BwH,mBAAmB,CACjBxH,KAAK,EACLyH,MAAM,EACNzL,cAAc,CAACsB,WAAW,CAACsD,CAAC,CAC9B,CAAC;IACD,IAAI8G,IAAI,GAAG,EAAE;IACb,IAAIC,CAAC,GAAG,CAAC,CAAC;IACV,OAAO,EAAEA,CAAC,GAAGF,MAAM,CAAC3G,MAAM,EAAE;MAC1B4G,IAAI,CAACC,CAAC,CAAC,GAAGF,MAAM,CAACE,CAAC,CAAC;IACrB;IACAF,MAAM,GAAGC,IAAI,CAAClG,IAAI,CAACxF,cAAc,CAACyF,cAAc,CAAC,CAC9Cc,MAAM,CAAC,UAAS+D,IAAI,EAAEsB,GAAG,EAAEC,GAAG,EAAE;MAC/B,OAAO,CAACD,GAAG,IAAItB,IAAI,KAAKuB,GAAG,CAACD,GAAG,GAAG,CAAC,CAAC;IACtC,CAAC,CAAC;IACJ,IAAIH,MAAM,CAAC3G,MAAM,KAAK4G,IAAI,CAAC5G,MAAM,EAAE;MACjC,MAAM,IAAIV,KAAK,CAAC,0BAA0B,GAAGJ,KAAK,GAAG,4BAA4B,CAAC;IACpF;IACAsC,YAAY,CAACtC,KAAK,CAAC,GAAGyH,MAAM;EAC9B;EACA,IAAIhI,UAAU,GAAGzD,cAAc,CAACqG,qBAAqB,CAACC,YAAY,CAAC;EACnEA,YAAY,CAAC7C,UAAU,GAAGA,UAAU,IAAI6C,YAAY,CAAC7C,UAAU;EAC/D,OAAO,IAAIzD,cAAc,CAACsG,YAAY,EAAEpG,OAAO,IAAI,CAAC,CAAC,CAAC;AACxD,CAAC;AAED4L,MAAM,CAACC,OAAO,GAAG/L,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}