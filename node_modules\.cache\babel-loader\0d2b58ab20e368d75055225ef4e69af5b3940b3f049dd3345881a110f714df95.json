{"ast": null, "code": "import React from\"react\";import ReactDOM from\"react-dom/client\";import\"./index.css\";import App from\"./App\";import{schedulePageRefresh}from\"../utils/scheduler\";import{jsx as _jsx,Fragment as _Fragment}from\"react/jsx-runtime\";const root=ReactDOM.createRoot(document.getElementById(\"root\"));// Start the scheduler\nschedulePageRefresh();root.render(/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsx(App,{})}));// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analyt", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "schedulePageRefresh", "jsx", "_jsx", "Fragment", "_Fragment", "root", "createRoot", "document", "getElementById", "render", "children"], "sources": ["C:/Users/<USER>/Documents/ekrani/src/index.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport ReactDOM from \"react-dom/client\";\r\nimport \"./index.css\";\r\nimport App from \"./App\";\r\nimport { schedulePageRefresh } from \"../utils/scheduler\";\r\nconst root = ReactDOM.createRoot(\r\n  document.getElementById(\"root\") as HTMLElement\r\n);\r\n// Start the scheduler\r\nschedulePageRefresh();\r\n\r\nroot.render(\r\n  <>\r\n    <App />\r\n  </>\r\n);\r\n\r\n\r\n// If you want to start measuring performance in your app, pass a function\r\n// to log results (for example: reportWebVitals(console.log))\r\n// or send to an analyt\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,aAAa,CACpB,MAAO,CAAAC,GAAG,KAAM,OAAO,CACvB,OAASC,mBAAmB,KAAQ,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,yBACzD,KAAM,CAAAC,IAAI,CAAGP,QAAQ,CAACQ,UAAU,CAC9BC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAChC,CAAC,CACD;AACAR,mBAAmB,CAAC,CAAC,CAErBK,IAAI,CAACI,MAAM,cACTP,IAAA,CAAAE,SAAA,EAAAM,QAAA,cACER,IAAA,CAACH,GAAG,GAAE,CAAC,CACP,CACJ,CAAC,CAGD;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}