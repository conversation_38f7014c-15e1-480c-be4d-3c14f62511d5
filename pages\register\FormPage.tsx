import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import "./form-style.css";
import Switch from "react-switch";
import IqamahCard from "../../components/common/IqamahCard/IqamahCard";

export interface FormDataStorage {
  name: string;
  location: string;
  mosqueName: string;
  attentionText: {
    title?: string;
    description?: string;
  };
  showIqamah: boolean;
  prayers: {
    [key: string]: {
      timeAfterAdhan?: number;
      timeBeforeSunRise?: number;
      showOnTV: boolean;
    };
  };
}

const FormPage = () => {
  const formData: FormDataStorage | null = JSON.parse(
    localStorage.getItem("formData") || "null"
  );

  const [name, setName] = useState(formData?.name || "");
  const [mosqueName, setMosqueName] = useState(formData?.mosqueName || "");
  const [location, setLocation] = useState(formData?.location || "");
  const [isIqamahOn, setIsIqamahOn] = useState(formData?.showIqamah || false);

  const [prayers, setPrayers] = useState<FormDataStorage["prayers"]>({
    dreka: formData?.prayers?.dreka || {
      timeAfterAdhan: 0,
      timeBeforeSunRise: 0,
      showOnTV: false,
    },
    ikindia: formData?.prayers?.ikindia || {
      timeAfterAdhan: 0,
      timeBeforeSunRise: 0,
      showOnTV: false,
    },
    akshami: formData?.prayers?.akshami || {
      timeAfterAdhan: 0,
      timeBeforeSunRise: 0,
      showOnTV: false,
    },
    jacia: formData?.prayers?.jacia || {
      timeAfterAdhan: 0,
      timeBeforeSunRise: 0,
      showOnTV: false,
    },
  });

  const navigate = useNavigate();


  const handleSubmit = (e: React.FormEvent) => {
    const formData: FormDataStorage | null = JSON.parse(
      localStorage.getItem("formData") || "null"
    );
    e.preventDefault();
    const data: FormDataStorage = {
      name,
      location,
      mosqueName,
      attentionText: formData?.attentionText || { title: "", description: "" },
      prayers: formData?.prayers || prayers,
      showIqamah: isIqamahOn,
    };
    localStorage.setItem("formData", JSON.stringify(data));
    setTimeout(() => {
      navigate("/");
    }, 0);
  };

  const toggleSwitch = () => {
    setIsIqamahOn((prev) => !prev);
  };

  return (
    <div
      style={{
        backgroundImage: `url(${require("../../assets/images/background-opacity.png")}), url(${require(`../../assets/images/wallpaper/${new Date().getDate()}.jpeg`)})`,
        minHeight: "100vh", 
        paddingBottom: 100, 
        backgroundSize: "cover",
        backgroundAttachment: "fixed", 
        backgroundPosition: "center", 
        backgroundRepeat: "no-repeat",
      }}
    >
      <div className="container">
        <div className="card">
          <h2>
            <i className="fas fa-edit"></i> Plotëso Formularin
          </h2>
          <br />
          <br />
          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="name">
                <i className="fas fa-user"></i> Imami:
              </label>
              <input
                type="text"
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
              />
            </div>
            <div className="form-group">
              <label htmlFor="mosqueName">
                <i className="fas fa-user"></i> Emri i Xhamise:
              </label>
              <input
                type="text"
                id="mosqueName"
                value={mosqueName}
                onChange={(e) => setMosqueName(e.target.value)}
              />
            </div>
            <div className="form-group">
              <label htmlFor="location">
                <i className="fas fa-map-marker-alt"></i> Lokacioni:
              </label>
              <input
                type="text"
                id="location"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
              />
            </div>

            {/* Prayers Section */}
            <label
              className="switch"
              style={{
                display: "flex",
                flexDirection: "row",
                marginBottom: 20,
              }}
            >
              <span
                style={{ fontSize: 16, marginTop: 2, width: 300 }}
                className="switch-status"
              >
                {"Shfaq Ikametin"}
              </span>
              <Switch
                color="darkcyan"
                uncheckedIcon
                onChange={toggleSwitch}
                checked={isIqamahOn}
              />
            </label>

            {isIqamahOn && (
              <div>
                <IqamahCard prayer="dreka" />
                <IqamahCard prayer="ikindia" />
                <IqamahCard prayer="akshami" />
                <IqamahCard prayer="jacia" />
              </div>
            )}

            <button type="submit" className="submit-button">
              <i className="fas fa-check"></i> Ruaje
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default FormPage;
