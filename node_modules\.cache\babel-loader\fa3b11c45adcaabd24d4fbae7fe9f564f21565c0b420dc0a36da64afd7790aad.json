{"ast": null, "code": "exports.add = add;\nexports.addFromFront = addFromFront;\nexports.remove = remove;\nexports.has = has;\nexports.eq = eq;\nexports.lte = lte;\nexports.lt = lt;\nexports.gte = gte;\nexports.gt = gt;\nexports.nearest = nearest;\nfunction defaultCmp(a, b) {\n  if (a === b) return 0;\n  return a < b ? -1 : 1;\n}\nfunction add(list, value, cmp) {\n  if (!cmp) cmp = defaultCmp;\n  var top = list.push(value) - 1;\n  while (top) {\n    if (cmp(list[top - 1], value) < 0) return;\n    list[top] = list[top - 1];\n    list[top - 1] = value;\n    top--;\n  }\n}\nfunction addFromFront(list, value, cmp) {\n  if (!cmp) cmp = defaultCmp;\n  var top = list.unshift(value) - 1;\n  for (var i = 0; i < top; i++) {\n    if (cmp(value, list[i + 1]) < 0) return;\n    list[i] = list[i + 1];\n    list[i + 1] = value;\n  }\n}\nfunction lte(list, value, cmp) {\n  if (!cmp) cmp = defaultCmp;\n  var i = indexOf(list, value, cmp);\n  if (i === -1) return -1;\n  for (; i >= 0; i--) {\n    var c = cmp(list[i], value);\n    if (c <= 0) return i;\n  }\n  return -1;\n}\nfunction lt(list, value, cmp) {\n  if (!cmp) cmp = defaultCmp;\n  var i = indexOf(list, value, cmp);\n  if (i === -1) return -1;\n  for (; i >= 0; i--) {\n    var c = cmp(list[i], value);\n    if (c < 0) return i;\n  }\n  return -1;\n}\nfunction gte(list, value, cmp) {\n  if (!cmp) cmp = defaultCmp;\n  var i = indexOf(list, value, cmp);\n  if (i === -1) return -1;\n  for (; i < list.length; i++) {\n    var c = cmp(list[i], value);\n    if (c >= 0) return i;\n  }\n  return -1;\n}\nfunction gt(list, value, cmp) {\n  if (!cmp) cmp = defaultCmp;\n  var i = indexOf(list, value, cmp);\n  if (i === -1) return -1;\n  for (; i < list.length; i++) {\n    var c = cmp(list[i], value);\n    if (c > 0) return i;\n  }\n  return -1;\n}\nfunction eq(list, value, cmp) {\n  if (!cmp) cmp = defaultCmp;\n  var i = indexOf(list, value, cmp);\n  if (i === -1) return -1;\n  return cmp(list[i], value) === 0 ? i : -1;\n}\nfunction nearest(list, value, cmp) {\n  if (!cmp) cmp = defaultCmp;\n  var len = list.length;\n  var top = len - 1;\n  var btm = 0;\n  var mid = -1;\n  var trending = 1; // 0 = down, 2 = up\n\n  while (top >= btm && btm >= 0 && top < len) {\n    mid = Math.floor((top + btm) / 2);\n    var c = cmp(list[mid], value);\n    if (c === 0) return mid;\n    if (c >= 0) {\n      if (trending === 1) trending = 0;else if (trending === 2) {\n        if (Math.abs(list[mid] - value) > Math.abs(list[mid - 1] - value)) return mid - 1;\n        return mid;\n      }\n      top = mid - 1;\n    } else {\n      if (trending === 1) trending = 2;else if (trending === 0) return mid;\n      btm = mid + 1;\n    }\n  }\n  return mid;\n}\nfunction indexOf(list, value, cmp) {\n  if (!cmp) cmp = defaultCmp;\n  var len = list.length;\n  var top = len - 1;\n  var btm = 0;\n  var mid = -1;\n  while (top >= btm && btm >= 0 && top < len) {\n    mid = Math.floor((top + btm) / 2);\n    var c = cmp(list[mid], value);\n    if (c === 0) return mid;\n    if (c >= 0) {\n      top = mid - 1;\n    } else {\n      btm = mid + 1;\n    }\n  }\n  return mid;\n}\nfunction has(list, value, cmp) {\n  return eq(list, value, cmp) > -1;\n}\nfunction remove(list, value, cmp) {\n  var i = eq(list, value, cmp);\n  if (i === -1) return false;\n  list.splice(i, 1);\n  return true;\n}", "map": {"version": 3, "names": ["exports", "add", "addFromFront", "remove", "has", "eq", "lte", "lt", "gte", "gt", "nearest", "defaultCmp", "a", "b", "list", "value", "cmp", "top", "push", "unshift", "i", "indexOf", "c", "length", "len", "btm", "mid", "trending", "Math", "floor", "abs", "splice"], "sources": ["C:/Users/<USER>/Documents/ekrani/node_modules/sorted-array-functions/index.js"], "sourcesContent": ["exports.add = add\nexports.addFromFront = addFromFront\nexports.remove = remove\nexports.has = has\nexports.eq = eq\nexports.lte = lte\nexports.lt = lt\nexports.gte = gte\nexports.gt = gt\nexports.nearest = nearest\n\nfunction defaultCmp (a, b) {\n  if (a === b) return 0\n  return a < b ? -1 : 1\n}\n\nfunction add (list, value, cmp) {\n  if (!cmp) cmp = defaultCmp\n\n  var top = list.push(value) - 1\n\n  while (top) {\n    if (cmp(list[top - 1], value) < 0) return\n    list[top] = list[top - 1]\n    list[top - 1] = value\n    top--\n  }\n}\n\nfunction addFromFront (list, value, cmp) {\n  if (!cmp) cmp = defaultCmp\n\n  var top = list.unshift(value) - 1\n\n  for (var i = 0; i < top; i++) {\n    if (cmp(value, list[i + 1]) < 0) return\n    list[i] = list[i + 1]\n    list[i + 1] = value\n  }\n}\n\nfunction lte (list, value, cmp) {\n  if (!cmp) cmp = defaultCmp\n\n  var i = indexOf(list, value, cmp)\n  if (i === -1) return -1\n\n  for (; i >= 0; i--) {\n    var c = cmp(list[i], value)\n    if (c <= 0) return i\n  }\n\n  return -1\n}\n\nfunction lt (list, value, cmp) {\n  if (!cmp) cmp = defaultCmp\n\n  var i = indexOf(list, value, cmp)\n  if (i === -1) return -1\n\n  for (; i >= 0; i--) {\n    var c = cmp(list[i], value)\n    if (c < 0) return i\n  }\n\n  return -1\n}\n\nfunction gte (list, value, cmp) {\n  if (!cmp) cmp = defaultCmp\n\n  var i = indexOf(list, value, cmp)\n  if (i === -1) return -1\n\n  for (; i < list.length; i++) {\n    var c = cmp(list[i], value)\n    if (c >= 0) return i\n  }\n\n  return -1\n}\n\nfunction gt (list, value, cmp) {\n  if (!cmp) cmp = defaultCmp\n\n  var i = indexOf(list, value, cmp)\n  if (i === -1) return -1\n\n  for (; i < list.length; i++) {\n    var c = cmp(list[i], value)\n    if (c > 0) return i\n  }\n\n  return -1\n}\n\nfunction eq (list, value, cmp) {\n  if (!cmp) cmp = defaultCmp\n\n  var i = indexOf(list, value, cmp)\n  if (i === -1) return -1\n  return cmp(list[i], value) === 0 ? i : -1\n}\n\nfunction nearest (list, value, cmp) {\n  if (!cmp) cmp = defaultCmp\n\n  var len = list.length\n  var top = len - 1\n  var btm = 0\n  var mid = -1\n  var trending = 1 // 0 = down, 2 = up\n\n  while (top >= btm && btm >= 0 && top < len) {\n    mid = Math.floor((top + btm) / 2)\n\n    var c = cmp(list[mid], value)\n    if (c === 0) return mid\n\n    if (c >= 0) {\n      if (trending === 1) trending = 0\n      else if (trending === 2) {\n        if (Math.abs(list[mid] - value) > Math.abs(list[mid - 1] - value)) return mid - 1\n        return mid\n      }\n\n      top = mid - 1\n    } else {\n      if (trending === 1) trending = 2\n      else if (trending === 0) return mid\n\n      btm = mid + 1\n    }\n  }\n\n  return mid\n}\n\nfunction indexOf (list, value, cmp) {\n  if (!cmp) cmp = defaultCmp\n\n  var len = list.length\n  var top = len - 1\n  var btm = 0\n  var mid = -1\n\n  while (top >= btm && btm >= 0 && top < len) {\n    mid = Math.floor((top + btm) / 2)\n\n    var c = cmp(list[mid], value)\n    if (c === 0) return mid\n\n    if (c >= 0) {\n      top = mid - 1\n    } else {\n      btm = mid + 1\n    }\n  }\n\n  return mid\n}\n\nfunction has (list, value, cmp) {\n  return eq(list, value, cmp) > -1\n}\n\nfunction remove (list, value, cmp) {\n  var i = eq(list, value, cmp)\n  if (i === -1) return false\n  list.splice(i, 1)\n  return true\n}\n"], "mappings": "AAAAA,OAAO,CAACC,GAAG,GAAGA,GAAG;AACjBD,OAAO,CAACE,YAAY,GAAGA,YAAY;AACnCF,OAAO,CAACG,MAAM,GAAGA,MAAM;AACvBH,OAAO,CAACI,GAAG,GAAGA,GAAG;AACjBJ,OAAO,CAACK,EAAE,GAAGA,EAAE;AACfL,OAAO,CAACM,GAAG,GAAGA,GAAG;AACjBN,OAAO,CAACO,EAAE,GAAGA,EAAE;AACfP,OAAO,CAACQ,GAAG,GAAGA,GAAG;AACjBR,OAAO,CAACS,EAAE,GAAGA,EAAE;AACfT,OAAO,CAACU,OAAO,GAAGA,OAAO;AAEzB,SAASC,UAAUA,CAAEC,CAAC,EAAEC,CAAC,EAAE;EACzB,IAAID,CAAC,KAAKC,CAAC,EAAE,OAAO,CAAC;EACrB,OAAOD,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AACvB;AAEA,SAASZ,GAAGA,CAAEa,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAE;EAC9B,IAAI,CAACA,GAAG,EAAEA,GAAG,GAAGL,UAAU;EAE1B,IAAIM,GAAG,GAAGH,IAAI,CAACI,IAAI,CAACH,KAAK,CAAC,GAAG,CAAC;EAE9B,OAAOE,GAAG,EAAE;IACV,IAAID,GAAG,CAACF,IAAI,CAACG,GAAG,GAAG,CAAC,CAAC,EAAEF,KAAK,CAAC,GAAG,CAAC,EAAE;IACnCD,IAAI,CAACG,GAAG,CAAC,GAAGH,IAAI,CAACG,GAAG,GAAG,CAAC,CAAC;IACzBH,IAAI,CAACG,GAAG,GAAG,CAAC,CAAC,GAAGF,KAAK;IACrBE,GAAG,EAAE;EACP;AACF;AAEA,SAASf,YAAYA,CAAEY,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAE;EACvC,IAAI,CAACA,GAAG,EAAEA,GAAG,GAAGL,UAAU;EAE1B,IAAIM,GAAG,GAAGH,IAAI,CAACK,OAAO,CAACJ,KAAK,CAAC,GAAG,CAAC;EAEjC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,EAAEG,CAAC,EAAE,EAAE;IAC5B,IAAIJ,GAAG,CAACD,KAAK,EAAED,IAAI,CAACM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;IACjCN,IAAI,CAACM,CAAC,CAAC,GAAGN,IAAI,CAACM,CAAC,GAAG,CAAC,CAAC;IACrBN,IAAI,CAACM,CAAC,GAAG,CAAC,CAAC,GAAGL,KAAK;EACrB;AACF;AAEA,SAAST,GAAGA,CAAEQ,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAE;EAC9B,IAAI,CAACA,GAAG,EAAEA,GAAG,GAAGL,UAAU;EAE1B,IAAIS,CAAC,GAAGC,OAAO,CAACP,IAAI,EAAEC,KAAK,EAAEC,GAAG,CAAC;EACjC,IAAII,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;EAEvB,OAAOA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAClB,IAAIE,CAAC,GAAGN,GAAG,CAACF,IAAI,CAACM,CAAC,CAAC,EAAEL,KAAK,CAAC;IAC3B,IAAIO,CAAC,IAAI,CAAC,EAAE,OAAOF,CAAC;EACtB;EAEA,OAAO,CAAC,CAAC;AACX;AAEA,SAASb,EAAEA,CAAEO,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAE;EAC7B,IAAI,CAACA,GAAG,EAAEA,GAAG,GAAGL,UAAU;EAE1B,IAAIS,CAAC,GAAGC,OAAO,CAACP,IAAI,EAAEC,KAAK,EAAEC,GAAG,CAAC;EACjC,IAAII,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;EAEvB,OAAOA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAClB,IAAIE,CAAC,GAAGN,GAAG,CAACF,IAAI,CAACM,CAAC,CAAC,EAAEL,KAAK,CAAC;IAC3B,IAAIO,CAAC,GAAG,CAAC,EAAE,OAAOF,CAAC;EACrB;EAEA,OAAO,CAAC,CAAC;AACX;AAEA,SAASZ,GAAGA,CAAEM,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAE;EAC9B,IAAI,CAACA,GAAG,EAAEA,GAAG,GAAGL,UAAU;EAE1B,IAAIS,CAAC,GAAGC,OAAO,CAACP,IAAI,EAAEC,KAAK,EAAEC,GAAG,CAAC;EACjC,IAAII,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;EAEvB,OAAOA,CAAC,GAAGN,IAAI,CAACS,MAAM,EAAEH,CAAC,EAAE,EAAE;IAC3B,IAAIE,CAAC,GAAGN,GAAG,CAACF,IAAI,CAACM,CAAC,CAAC,EAAEL,KAAK,CAAC;IAC3B,IAAIO,CAAC,IAAI,CAAC,EAAE,OAAOF,CAAC;EACtB;EAEA,OAAO,CAAC,CAAC;AACX;AAEA,SAASX,EAAEA,CAAEK,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAE;EAC7B,IAAI,CAACA,GAAG,EAAEA,GAAG,GAAGL,UAAU;EAE1B,IAAIS,CAAC,GAAGC,OAAO,CAACP,IAAI,EAAEC,KAAK,EAAEC,GAAG,CAAC;EACjC,IAAII,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;EAEvB,OAAOA,CAAC,GAAGN,IAAI,CAACS,MAAM,EAAEH,CAAC,EAAE,EAAE;IAC3B,IAAIE,CAAC,GAAGN,GAAG,CAACF,IAAI,CAACM,CAAC,CAAC,EAAEL,KAAK,CAAC;IAC3B,IAAIO,CAAC,GAAG,CAAC,EAAE,OAAOF,CAAC;EACrB;EAEA,OAAO,CAAC,CAAC;AACX;AAEA,SAASf,EAAEA,CAAES,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAE;EAC7B,IAAI,CAACA,GAAG,EAAEA,GAAG,GAAGL,UAAU;EAE1B,IAAIS,CAAC,GAAGC,OAAO,CAACP,IAAI,EAAEC,KAAK,EAAEC,GAAG,CAAC;EACjC,IAAII,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;EACvB,OAAOJ,GAAG,CAACF,IAAI,CAACM,CAAC,CAAC,EAAEL,KAAK,CAAC,KAAK,CAAC,GAAGK,CAAC,GAAG,CAAC,CAAC;AAC3C;AAEA,SAASV,OAAOA,CAAEI,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAE;EAClC,IAAI,CAACA,GAAG,EAAEA,GAAG,GAAGL,UAAU;EAE1B,IAAIa,GAAG,GAAGV,IAAI,CAACS,MAAM;EACrB,IAAIN,GAAG,GAAGO,GAAG,GAAG,CAAC;EACjB,IAAIC,GAAG,GAAG,CAAC;EACX,IAAIC,GAAG,GAAG,CAAC,CAAC;EACZ,IAAIC,QAAQ,GAAG,CAAC,EAAC;;EAEjB,OAAOV,GAAG,IAAIQ,GAAG,IAAIA,GAAG,IAAI,CAAC,IAAIR,GAAG,GAAGO,GAAG,EAAE;IAC1CE,GAAG,GAAGE,IAAI,CAACC,KAAK,CAAC,CAACZ,GAAG,GAAGQ,GAAG,IAAI,CAAC,CAAC;IAEjC,IAAIH,CAAC,GAAGN,GAAG,CAACF,IAAI,CAACY,GAAG,CAAC,EAAEX,KAAK,CAAC;IAC7B,IAAIO,CAAC,KAAK,CAAC,EAAE,OAAOI,GAAG;IAEvB,IAAIJ,CAAC,IAAI,CAAC,EAAE;MACV,IAAIK,QAAQ,KAAK,CAAC,EAAEA,QAAQ,GAAG,CAAC,MAC3B,IAAIA,QAAQ,KAAK,CAAC,EAAE;QACvB,IAAIC,IAAI,CAACE,GAAG,CAAChB,IAAI,CAACY,GAAG,CAAC,GAAGX,KAAK,CAAC,GAAGa,IAAI,CAACE,GAAG,CAAChB,IAAI,CAACY,GAAG,GAAG,CAAC,CAAC,GAAGX,KAAK,CAAC,EAAE,OAAOW,GAAG,GAAG,CAAC;QACjF,OAAOA,GAAG;MACZ;MAEAT,GAAG,GAAGS,GAAG,GAAG,CAAC;IACf,CAAC,MAAM;MACL,IAAIC,QAAQ,KAAK,CAAC,EAAEA,QAAQ,GAAG,CAAC,MAC3B,IAAIA,QAAQ,KAAK,CAAC,EAAE,OAAOD,GAAG;MAEnCD,GAAG,GAAGC,GAAG,GAAG,CAAC;IACf;EACF;EAEA,OAAOA,GAAG;AACZ;AAEA,SAASL,OAAOA,CAAEP,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAE;EAClC,IAAI,CAACA,GAAG,EAAEA,GAAG,GAAGL,UAAU;EAE1B,IAAIa,GAAG,GAAGV,IAAI,CAACS,MAAM;EACrB,IAAIN,GAAG,GAAGO,GAAG,GAAG,CAAC;EACjB,IAAIC,GAAG,GAAG,CAAC;EACX,IAAIC,GAAG,GAAG,CAAC,CAAC;EAEZ,OAAOT,GAAG,IAAIQ,GAAG,IAAIA,GAAG,IAAI,CAAC,IAAIR,GAAG,GAAGO,GAAG,EAAE;IAC1CE,GAAG,GAAGE,IAAI,CAACC,KAAK,CAAC,CAACZ,GAAG,GAAGQ,GAAG,IAAI,CAAC,CAAC;IAEjC,IAAIH,CAAC,GAAGN,GAAG,CAACF,IAAI,CAACY,GAAG,CAAC,EAAEX,KAAK,CAAC;IAC7B,IAAIO,CAAC,KAAK,CAAC,EAAE,OAAOI,GAAG;IAEvB,IAAIJ,CAAC,IAAI,CAAC,EAAE;MACVL,GAAG,GAAGS,GAAG,GAAG,CAAC;IACf,CAAC,MAAM;MACLD,GAAG,GAAGC,GAAG,GAAG,CAAC;IACf;EACF;EAEA,OAAOA,GAAG;AACZ;AAEA,SAAStB,GAAGA,CAAEU,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAE;EAC9B,OAAOX,EAAE,CAACS,IAAI,EAAEC,KAAK,EAAEC,GAAG,CAAC,GAAG,CAAC,CAAC;AAClC;AAEA,SAASb,MAAMA,CAAEW,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAE;EACjC,IAAII,CAAC,GAAGf,EAAE,CAACS,IAAI,EAAEC,KAAK,EAAEC,GAAG,CAAC;EAC5B,IAAII,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,KAAK;EAC1BN,IAAI,CAACiB,MAAM,CAACX,CAAC,EAAE,CAAC,CAAC;EACjB,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}