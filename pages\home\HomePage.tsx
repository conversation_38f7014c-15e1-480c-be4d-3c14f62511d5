import { useNavigate } from "react-router-dom";
import "./home-style.css";
import PrayerTimesHolder from "../../components/prayer-times-holder/prayer-times-holder";
import HadithHolder from "../../components/hadith-holder/hadith-holder";
import { useEffect, useState } from "react";
import { setupMidnightInterval } from "../../utils/mid-night";
import { getTodayPrayerTimes } from "../../utils/prayer-times.service";
import { Prayer } from "../../utils/prayer.model";

export const HomePage = () => {
  const navigate = useNavigate();
  const [data, setData] = useState<Prayer | null>(null);
  const [wallpaper, setWallpaper] = useState<string>("");

  // Check for form data and redirect to form if not available
  useEffect(() => {
    const haveData = JSON.parse(localStorage.getItem("formData") || "null");

    if (!haveData) {
      navigate("/form");
      return;
    }
  }, [navigate]);

  const getWallpaper = () => {
    const dayOfMonth = new Date().getDate();
    setWallpaper(require(`../../assets/images/wallpaper/${dayOfMonth}.jpeg`));
  };

  // Callback function to re-setup the interval
  const intervalCallback = () => {
    setupMidnightInterval(getWallpaper, intervalCallback);
  };

  // Fetch prayer times and wallpaper setup
  useEffect(() => {
    const fetchPrayerTimes = async () => {
      const todayPrayerTimes = await getTodayPrayerTimes() as Prayer;
      setData(todayPrayerTimes);
    };

    fetchPrayerTimes();
    getWallpaper();

    // Set up midnight interval for updating wallpaper
    const cleanup = setupMidnightInterval(getWallpaper, intervalCallback);

    return () => {
      cleanup(); // Cleanup on component unmount
    };
  }, []);

  // Return null if data hasn't loaded yet
  if (!data) return null;

  return (
    <div
      className="home-container"
      style={{
        backgroundImage: `url(${require("../../assets/images/background-opacity.png")}), url(${wallpaper})`,
      }}
    >
      <HadithHolder />
      <PrayerTimesHolder />
    </div>
  );
};
