{"ast": null, "code": "var TIMEOUT_MAX = 2147483647; // 2^31-1\n\nexports.setTimeout = function (listener, after) {\n  return new Timeout(listener, after);\n};\nexports.setInterval = function (listener, after) {\n  return new Interval(listener, after);\n};\nexports.clearTimeout = function (timer) {\n  if (timer) timer.close();\n};\nexports.clearInterval = exports.clearTimeout;\nexports.Timeout = Timeout;\nexports.Interval = Interval;\nfunction Timeout(listener, after) {\n  this.listener = listener;\n  this.after = after;\n  this.unreffed = false;\n  this.start();\n}\nTimeout.prototype.unref = function () {\n  if (!this.unreffed) {\n    this.unreffed = true;\n    this.timeout.unref();\n  }\n};\nTimeout.prototype.ref = function () {\n  if (this.unreffed) {\n    this.unreffed = false;\n    this.timeout.ref();\n  }\n};\nTimeout.prototype.start = function () {\n  if (this.after <= TIMEOUT_MAX) {\n    this.timeout = setTimeout(this.listener, this.after);\n  } else {\n    var self = this;\n    this.timeout = setTimeout(function () {\n      self.after -= TIMEOUT_MAX;\n      self.start();\n    }, TIMEOUT_MAX);\n  }\n  if (this.unreffed) {\n    this.timeout.unref();\n  }\n};\nTimeout.prototype.close = function () {\n  clearTimeout(this.timeout);\n};\nfunction Interval(listener, after) {\n  this.listener = listener;\n  this.after = this.timeLeft = after;\n  this.unreffed = false;\n  this.start();\n}\nInterval.prototype.unref = function () {\n  if (!this.unreffed) {\n    this.unreffed = true;\n    this.timeout.unref();\n  }\n};\nInterval.prototype.ref = function () {\n  if (this.unreffed) {\n    this.unreffed = false;\n    this.timeout.ref();\n  }\n};\nInterval.prototype.start = function () {\n  var self = this;\n  if (this.timeLeft <= TIMEOUT_MAX) {\n    this.timeout = setTimeout(function () {\n      self.listener();\n      self.timeLeft = self.after;\n      self.start();\n    }, this.timeLeft);\n  } else {\n    this.timeout = setTimeout(function () {\n      self.timeLeft -= TIMEOUT_MAX;\n      self.start();\n    }, TIMEOUT_MAX);\n  }\n  if (this.unreffed) {\n    this.timeout.unref();\n  }\n};\nInterval.prototype.close = function () {\n  Timeout.prototype.close.apply(this, arguments);\n};", "map": {"version": 3, "names": ["TIMEOUT_MAX", "exports", "setTimeout", "listener", "after", "Timeout", "setInterval", "Interval", "clearTimeout", "timer", "close", "clearInterval", "unreffed", "start", "prototype", "unref", "timeout", "ref", "self", "timeLeft", "apply", "arguments"], "sources": ["C:/Users/<USER>/Documents/ekrani/node_modules/long-timeout/index.js"], "sourcesContent": ["\nvar TIMEOUT_MAX = 2147483647; // 2^31-1\n\nexports.setTimeout = function(listener, after) {\n  return new Timeout(listener, after)\n}\nexports.setInterval = function(listener, after) {\n  return new Interval(listener, after)\n}\nexports.clearTimeout = function(timer) {\n  if (timer) timer.close()\n}\nexports.clearInterval = exports.clearTimeout\n\nexports.Timeout = Timeout\nexports.Interval = Interval\n\nfunction Timeout(listener, after) {\n  this.listener = listener\n  this.after = after\n  this.unreffed = false\n  this.start()\n}\n\nTimeout.prototype.unref = function() {\n  if (!this.unreffed) {\n    this.unreffed = true\n    this.timeout.unref()\n  }\n}\n\nTimeout.prototype.ref = function() {\n  if (this.unreffed) {\n    this.unreffed = false\n    this.timeout.ref()\n  }\n}\n\nTimeout.prototype.start = function() {\n  if (this.after <= TIMEOUT_MAX) {\n    this.timeout = setTimeout(this.listener, this.after)\n  } else {\n    var self = this\n    this.timeout = setTimeout(function() {\n      self.after -= TIMEOUT_MAX\n      self.start()\n    }, TIMEOUT_MAX)\n  }\n  if (this.unreffed) {\n    this.timeout.unref()\n  }\n}\n\nTimeout.prototype.close = function() {\n  clearTimeout(this.timeout)\n}\n\nfunction Interval(listener, after) {\n  this.listener = listener\n  this.after = this.timeLeft = after\n  this.unreffed = false\n  this.start()\n}\n\nInterval.prototype.unref = function() {\n  if (!this.unreffed) {\n    this.unreffed = true\n    this.timeout.unref()\n  }\n}\n\nInterval.prototype.ref = function() {\n  if (this.unreffed) {\n    this.unreffed = false\n    this.timeout.ref()\n  }\n}\n\nInterval.prototype.start = function() {\n  var self = this\n\n  if (this.timeLeft <= TIMEOUT_MAX) {\n    this.timeout = setTimeout(function() {\n      self.listener()\n      self.timeLeft = self.after\n      self.start()\n    }, this.timeLeft)\n  } else {\n    this.timeout = setTimeout(function() {\n      self.timeLeft -= TIMEOUT_MAX\n      self.start()\n    }, TIMEOUT_MAX)\n  }\n  if (this.unreffed) {\n    this.timeout.unref()\n  }\n}\n\nInterval.prototype.close = function() {\n  Timeout.prototype.close.apply(this, arguments)\n}\n"], "mappings": "AACA,IAAIA,WAAW,GAAG,UAAU,CAAC,CAAC;;AAE9BC,OAAO,CAACC,UAAU,GAAG,UAASC,QAAQ,EAAEC,KAAK,EAAE;EAC7C,OAAO,IAAIC,OAAO,CAACF,QAAQ,EAAEC,KAAK,CAAC;AACrC,CAAC;AACDH,OAAO,CAACK,WAAW,GAAG,UAASH,QAAQ,EAAEC,KAAK,EAAE;EAC9C,OAAO,IAAIG,QAAQ,CAACJ,QAAQ,EAAEC,KAAK,CAAC;AACtC,CAAC;AACDH,OAAO,CAACO,YAAY,GAAG,UAASC,KAAK,EAAE;EACrC,IAAIA,KAAK,EAAEA,KAAK,CAACC,KAAK,CAAC,CAAC;AAC1B,CAAC;AACDT,OAAO,CAACU,aAAa,GAAGV,OAAO,CAACO,YAAY;AAE5CP,OAAO,CAACI,OAAO,GAAGA,OAAO;AACzBJ,OAAO,CAACM,QAAQ,GAAGA,QAAQ;AAE3B,SAASF,OAAOA,CAACF,QAAQ,EAAEC,KAAK,EAAE;EAChC,IAAI,CAACD,QAAQ,GAAGA,QAAQ;EACxB,IAAI,CAACC,KAAK,GAAGA,KAAK;EAClB,IAAI,CAACQ,QAAQ,GAAG,KAAK;EACrB,IAAI,CAACC,KAAK,CAAC,CAAC;AACd;AAEAR,OAAO,CAACS,SAAS,CAACC,KAAK,GAAG,YAAW;EACnC,IAAI,CAAC,IAAI,CAACH,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACI,OAAO,CAACD,KAAK,CAAC,CAAC;EACtB;AACF,CAAC;AAEDV,OAAO,CAACS,SAAS,CAACG,GAAG,GAAG,YAAW;EACjC,IAAI,IAAI,CAACL,QAAQ,EAAE;IACjB,IAAI,CAACA,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACI,OAAO,CAACC,GAAG,CAAC,CAAC;EACpB;AACF,CAAC;AAEDZ,OAAO,CAACS,SAAS,CAACD,KAAK,GAAG,YAAW;EACnC,IAAI,IAAI,CAACT,KAAK,IAAIJ,WAAW,EAAE;IAC7B,IAAI,CAACgB,OAAO,GAAGd,UAAU,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,KAAK,CAAC;EACtD,CAAC,MAAM;IACL,IAAIc,IAAI,GAAG,IAAI;IACf,IAAI,CAACF,OAAO,GAAGd,UAAU,CAAC,YAAW;MACnCgB,IAAI,CAACd,KAAK,IAAIJ,WAAW;MACzBkB,IAAI,CAACL,KAAK,CAAC,CAAC;IACd,CAAC,EAAEb,WAAW,CAAC;EACjB;EACA,IAAI,IAAI,CAACY,QAAQ,EAAE;IACjB,IAAI,CAACI,OAAO,CAACD,KAAK,CAAC,CAAC;EACtB;AACF,CAAC;AAEDV,OAAO,CAACS,SAAS,CAACJ,KAAK,GAAG,YAAW;EACnCF,YAAY,CAAC,IAAI,CAACQ,OAAO,CAAC;AAC5B,CAAC;AAED,SAAST,QAAQA,CAACJ,QAAQ,EAAEC,KAAK,EAAE;EACjC,IAAI,CAACD,QAAQ,GAAGA,QAAQ;EACxB,IAAI,CAACC,KAAK,GAAG,IAAI,CAACe,QAAQ,GAAGf,KAAK;EAClC,IAAI,CAACQ,QAAQ,GAAG,KAAK;EACrB,IAAI,CAACC,KAAK,CAAC,CAAC;AACd;AAEAN,QAAQ,CAACO,SAAS,CAACC,KAAK,GAAG,YAAW;EACpC,IAAI,CAAC,IAAI,CAACH,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACI,OAAO,CAACD,KAAK,CAAC,CAAC;EACtB;AACF,CAAC;AAEDR,QAAQ,CAACO,SAAS,CAACG,GAAG,GAAG,YAAW;EAClC,IAAI,IAAI,CAACL,QAAQ,EAAE;IACjB,IAAI,CAACA,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACI,OAAO,CAACC,GAAG,CAAC,CAAC;EACpB;AACF,CAAC;AAEDV,QAAQ,CAACO,SAAS,CAACD,KAAK,GAAG,YAAW;EACpC,IAAIK,IAAI,GAAG,IAAI;EAEf,IAAI,IAAI,CAACC,QAAQ,IAAInB,WAAW,EAAE;IAChC,IAAI,CAACgB,OAAO,GAAGd,UAAU,CAAC,YAAW;MACnCgB,IAAI,CAACf,QAAQ,CAAC,CAAC;MACfe,IAAI,CAACC,QAAQ,GAAGD,IAAI,CAACd,KAAK;MAC1Bc,IAAI,CAACL,KAAK,CAAC,CAAC;IACd,CAAC,EAAE,IAAI,CAACM,QAAQ,CAAC;EACnB,CAAC,MAAM;IACL,IAAI,CAACH,OAAO,GAAGd,UAAU,CAAC,YAAW;MACnCgB,IAAI,CAACC,QAAQ,IAAInB,WAAW;MAC5BkB,IAAI,CAACL,KAAK,CAAC,CAAC;IACd,CAAC,EAAEb,WAAW,CAAC;EACjB;EACA,IAAI,IAAI,CAACY,QAAQ,EAAE;IACjB,IAAI,CAACI,OAAO,CAACD,KAAK,CAAC,CAAC;EACtB;AACF,CAAC;AAEDR,QAAQ,CAACO,SAAS,CAACJ,KAAK,GAAG,YAAW;EACpCL,OAAO,CAACS,SAAS,CAACJ,KAAK,CAACU,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;AAChD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}