export function formatIslamicDate(date: any) {
  const formatter = new Intl.DateTimeFormat("en-GB", {
    day: "numeric",
    month: "numeric",
    year: "numeric",
    calendar: "islamic-umalqura",
  });

  const formattedParts = formatter.formatToParts(date);

  // Customize the month names
  const monthNames = [
    " Muharrem ",
    " Safer ",
    " Rebiul Euel ",
    " Rebiul Ahir ",
    " Xhumadel Euel ",
    " Xhumadel Ahir ",
    " <PERSON><PERSON><PERSON> ",
    " <PERSON><PERSON><PERSON> ",
    " <PERSON><PERSON> ",
    " <PERSON><PERSON><PERSON> ",
    " <PERSON>hul Kaade ",
    " Dhul Hixhe ",
  ];

  const formattedDateParts = formattedParts.map((part) => {
    if (part.type === "month") {
      const monthIndex = parseInt(part.value, 10) - 1;
      return { type: part.type, value: monthNames[monthIndex] };
    }
    return part;
  });
  const formattedDate = formattedDateParts.map((part) => part.value).join("");

  return formattedDate;
}
