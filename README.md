# Ekrani - Prayer Times Display Application

A React/TypeScript application for displaying Islamic prayer times with a beautiful interface designed for mosque displays.

## Features

- **Prayer Times Display**: Shows daily prayer times for Kosovo
- **Islamic Calendar**: Displays current Islamic (Hijri) date
- **Hadith Display**: Rotates through Islamic hadiths
- **Iqamah Times**: Configurable prayer call times
- **Dynamic Wallpapers**: Changes wallpaper daily
- **Responsive Design**: Optimized for large displays
- **Multilingual**: Supports Albanian language

## Technology Stack

- **React 18** - Frontend framework
- **TypeScript** - Type safety
- **React Router** - Navigation
- **Moment.js** - Date/time handling
- **Node Schedule** - Automated scheduling
- **CSS3** - Styling

## Project Structure

```
ekrani/
├── src/
│   ├── App.tsx
│   ├── index.tsx
│   └── main.js
├── components/
│   ├── common/
│   ├── hadith-holder/
│   ├── header/
│   └── prayer-times-holder/
├── pages/
│   ├── home/
│   ├── register/
│   └── settings/
├── utils/
│   ├── format-hijri.ts
│   ├── mid-night.ts
│   ├── prayer-lable.enum.ts
│   ├── prayer-times.service.ts
│   ├── prayer.model.ts
│   └── scheduler.ts
├── data/
│   ├── KS.json
│   └── hadiths.json
└── assets/
    ├── images/
    └── icons/
```

## Getting Started

### Prerequisites

- Node.js (version 16 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd ekrani
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm start
```

4. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

### Available Scripts

- `npm start` - Runs the app in development mode
- `npm build` - Builds the app for production
- `npm test` - Launches the test runner
- `npm eject` - Ejects from Create React App (one-way operation)

## Configuration

### Prayer Times Data

Prayer times are stored in `data/KS.json`. Update this file with accurate prayer times for your location.

### Hadiths

Islamic hadiths are stored in `data/hadiths.json`. You can add or modify hadiths as needed.

### Wallpapers

Daily wallpapers should be placed in `assets/images/wallpaper/` directory, numbered 1.jpeg through 31.jpeg for each day of the month.

## Usage

1. **Initial Setup**: Navigate to `/form` to configure mosque information and prayer settings
2. **Home Display**: The main display shows current prayer times, Islamic date, and rotating hadiths
3. **Settings**: Access `/settings` to modify configuration

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please open an issue in the repository.
