{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ekrani\\\\src\\\\index.tsx\";\nimport React from \"react\";\nimport ReactDOM from \"react-dom/client\";\nimport \"./index.css\";\nimport App from \"./App\";\nimport { schedulePageRefresh } from \"./utils/scheduler\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst root = ReactDOM.createRoot(document.getElementById(\"root\"));\n// Start the scheduler\nschedulePageRefresh();\nroot.render(/*#__PURE__*/_jsxDEV(_Fragment, {\n  children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this)\n}, void 0, false));\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analyt", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "schedulePageRefresh", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "root", "createRoot", "document", "getElementById", "render", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["C:/Users/<USER>/Documents/ekrani/src/index.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport ReactDOM from \"react-dom/client\";\r\nimport \"./index.css\";\r\nimport App from \"./App\";\r\nimport { schedulePageRefresh } from \"./utils/scheduler\";\r\nconst root = ReactDOM.createRoot(\r\n  document.getElementById(\"root\") as HTMLElement\r\n);\r\n// Start the scheduler\r\nschedulePageRefresh();\r\n\r\nroot.render(\r\n  <>\r\n    <App />\r\n  </>\r\n);\r\n\r\n\r\n// If you want to start measuring performance in your app, pass a function\r\n// to log results (for example: reportWebVitals(console.log))\r\n// or send to an analyt\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAO,aAAa;AACpB,OAAOC,GAAG,MAAM,OAAO;AACvB,SAASC,mBAAmB,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACxD,MAAMC,IAAI,GAAGP,QAAQ,CAACQ,UAAU,CAC9BC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAChC,CAAC;AACD;AACAR,mBAAmB,CAAC,CAAC;AAErBK,IAAI,CAACI,MAAM,cACTP,OAAA,CAAAE,SAAA;EAAAM,QAAA,eACER,OAAA,CAACH,GAAG;IAAAY,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC,gBACP,CACJ,CAAC;;AAGD;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}