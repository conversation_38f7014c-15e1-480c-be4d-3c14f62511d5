export interface PrayerNames {
  <PERSON>aj<PERSON>?: number;
  Data?: number;
  Imsaku: string;
  Sabahu: string;
  "<PERSON><PERSON> e <PERSON>": string;
  Dreka: string;
  Ikindia: string;
  Akshami: string;
  <PERSON>acia: string;
  [key: string]: any;
}

export interface Prayer {
  todayPrayerTimes: PrayerNames;
  nextImsaku: string;
}

export interface PrayerTimeData {
  Muaji: number;
  Data: number;
  Imsaku: string;
  Sabahu: string;
  "<PERSON>. e <PERSON>": string;
  Dreka: string;
  Ikindia: string;
  <PERSON>kshami: string;
  <PERSON>acia: string;
}
