{"ast": null, "code": "'use strict';\n\nvar compactField = require('./field_compactor');\nfunction stringifyField(arr, min, max) {\n  var ranges = compactField(arr);\n  if (ranges.length === 1) {\n    var singleRange = ranges[0];\n    var step = singleRange.step;\n    if (step === 1 && singleRange.start === min && singleRange.end === max) {\n      return '*';\n    }\n    if (step !== 1 && singleRange.start === min && singleRange.end === max - step + 1) {\n      return '*/' + step;\n    }\n  }\n  var result = [];\n  for (var i = 0, l = ranges.length; i < l; ++i) {\n    var range = ranges[i];\n    if (range.count === 1) {\n      result.push(range.start);\n      continue;\n    }\n    var step = range.step;\n    if (range.step === 1) {\n      result.push(range.start + '-' + range.end);\n      continue;\n    }\n    var multiplier = range.start == 0 ? range.count - 1 : range.count;\n    if (range.step * multiplier > range.end) {\n      result = result.concat(Array.from({\n        length: range.end - range.start + 1\n      }).map(function (_, index) {\n        var value = range.start + index;\n        if ((value - range.start) % range.step === 0) {\n          return value;\n        }\n        return null;\n      }).filter(function (value) {\n        return value != null;\n      }));\n    } else if (range.end === max - range.step + 1) {\n      result.push(range.start + '/' + range.step);\n    } else {\n      result.push(range.start + '-' + range.end + '/' + range.step);\n    }\n  }\n  return result.join(',');\n}\nmodule.exports = stringifyField;", "map": {"version": 3, "names": ["compactField", "require", "stringifyField", "arr", "min", "max", "ranges", "length", "singleRange", "step", "start", "end", "result", "i", "l", "range", "count", "push", "multiplier", "concat", "Array", "from", "map", "_", "index", "value", "filter", "join", "module", "exports"], "sources": ["C:/Users/<USER>/Documents/ekrani/node_modules/cron-parser/lib/field_stringify.js"], "sourcesContent": ["'use strict';\n\nvar compactField = require('./field_compactor');\n\nfunction stringifyField(arr, min, max) {\n  var ranges = compactField(arr);\n  if (ranges.length === 1) {\n    var singleRange = ranges[0];\n    var step = singleRange.step;\n    if (step === 1 && singleRange.start === min && singleRange.end === max) {\n      return '*';\n    }\n    if (step !== 1 && singleRange.start === min && singleRange.end === max - step + 1) {\n      return '*/' + step;\n    }\n  }\n\n  var result = [];\n  for (var i = 0, l = ranges.length; i < l; ++i) {\n    var range = ranges[i];\n    if (range.count === 1) {\n      result.push(range.start);\n      continue;\n    }\n\n    var step = range.step;\n    if (range.step === 1) {\n      result.push(range.start + '-' + range.end);\n      continue;\n    }\n\n    var multiplier = range.start == 0 ? range.count - 1 : range.count;\n    if (range.step * multiplier > range.end) {\n      result = result.concat(\n         Array\n          .from({ length: range.end - range.start + 1 })\n          .map(function (_, index) {\n            var value = range.start + index;\n            if ((value - range.start) % range.step === 0) {\n              return value;\n            }\n            return null;\n          })\n          .filter(function (value) {\n            return value != null;\n          })\n      );\n    } else if (range.end === max - range.step + 1) {\n      result.push(range.start + '/' + range.step);\n    } else {\n      result.push(range.start + '-' + range.end + '/' + range.step);\n    }\n  }\n\n  return result.join(',');\n}\n\nmodule.exports = stringifyField;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,mBAAmB,CAAC;AAE/C,SAASC,cAAcA,CAACC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAE;EACrC,IAAIC,MAAM,GAAGN,YAAY,CAACG,GAAG,CAAC;EAC9B,IAAIG,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;IACvB,IAAIC,WAAW,GAAGF,MAAM,CAAC,CAAC,CAAC;IAC3B,IAAIG,IAAI,GAAGD,WAAW,CAACC,IAAI;IAC3B,IAAIA,IAAI,KAAK,CAAC,IAAID,WAAW,CAACE,KAAK,KAAKN,GAAG,IAAII,WAAW,CAACG,GAAG,KAAKN,GAAG,EAAE;MACtE,OAAO,GAAG;IACZ;IACA,IAAII,IAAI,KAAK,CAAC,IAAID,WAAW,CAACE,KAAK,KAAKN,GAAG,IAAII,WAAW,CAACG,GAAG,KAAKN,GAAG,GAAGI,IAAI,GAAG,CAAC,EAAE;MACjF,OAAO,IAAI,GAAGA,IAAI;IACpB;EACF;EAEA,IAAIG,MAAM,GAAG,EAAE;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGR,MAAM,CAACC,MAAM,EAAEM,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;IAC7C,IAAIE,KAAK,GAAGT,MAAM,CAACO,CAAC,CAAC;IACrB,IAAIE,KAAK,CAACC,KAAK,KAAK,CAAC,EAAE;MACrBJ,MAAM,CAACK,IAAI,CAACF,KAAK,CAACL,KAAK,CAAC;MACxB;IACF;IAEA,IAAID,IAAI,GAAGM,KAAK,CAACN,IAAI;IACrB,IAAIM,KAAK,CAACN,IAAI,KAAK,CAAC,EAAE;MACpBG,MAAM,CAACK,IAAI,CAACF,KAAK,CAACL,KAAK,GAAG,GAAG,GAAGK,KAAK,CAACJ,GAAG,CAAC;MAC1C;IACF;IAEA,IAAIO,UAAU,GAAGH,KAAK,CAACL,KAAK,IAAI,CAAC,GAAGK,KAAK,CAACC,KAAK,GAAG,CAAC,GAAGD,KAAK,CAACC,KAAK;IACjE,IAAID,KAAK,CAACN,IAAI,GAAGS,UAAU,GAAGH,KAAK,CAACJ,GAAG,EAAE;MACvCC,MAAM,GAAGA,MAAM,CAACO,MAAM,CACnBC,KAAK,CACHC,IAAI,CAAC;QAAEd,MAAM,EAAEQ,KAAK,CAACJ,GAAG,GAAGI,KAAK,CAACL,KAAK,GAAG;MAAE,CAAC,CAAC,CAC7CY,GAAG,CAAC,UAAUC,CAAC,EAAEC,KAAK,EAAE;QACvB,IAAIC,KAAK,GAAGV,KAAK,CAACL,KAAK,GAAGc,KAAK;QAC/B,IAAI,CAACC,KAAK,GAAGV,KAAK,CAACL,KAAK,IAAIK,KAAK,CAACN,IAAI,KAAK,CAAC,EAAE;UAC5C,OAAOgB,KAAK;QACd;QACA,OAAO,IAAI;MACb,CAAC,CAAC,CACDC,MAAM,CAAC,UAAUD,KAAK,EAAE;QACvB,OAAOA,KAAK,IAAI,IAAI;MACtB,CAAC,CACL,CAAC;IACH,CAAC,MAAM,IAAIV,KAAK,CAACJ,GAAG,KAAKN,GAAG,GAAGU,KAAK,CAACN,IAAI,GAAG,CAAC,EAAE;MAC7CG,MAAM,CAACK,IAAI,CAACF,KAAK,CAACL,KAAK,GAAG,GAAG,GAAGK,KAAK,CAACN,IAAI,CAAC;IAC7C,CAAC,MAAM;MACLG,MAAM,CAACK,IAAI,CAACF,KAAK,CAACL,KAAK,GAAG,GAAG,GAAGK,KAAK,CAACJ,GAAG,GAAG,GAAG,GAAGI,KAAK,CAACN,IAAI,CAAC;IAC/D;EACF;EAEA,OAAOG,MAAM,CAACe,IAAI,CAAC,GAAG,CAAC;AACzB;AAEAC,MAAM,CAACC,OAAO,GAAG3B,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}