{"ast": null, "code": "'use strict';\n\nconst events = require('events');\nconst cronParser = require('cron-parser');\nconst CronDate = require('cron-parser/lib/date');\nconst sorted = require('sorted-array-functions');\nconst {\n  scheduleNextRecurrence,\n  scheduleInvocation,\n  cancelInvocation,\n  RecurrenceRule,\n  sorter,\n  Invocation\n} = require('./Invocation');\nconst {\n  isValidDate\n} = require('./utils/dateUtils');\nconst scheduledJobs = {};\nlet anonJobCounter = 0;\nfunction resolveAnonJobName() {\n  const now = new Date();\n  if (anonJobCounter === Number.MAX_SAFE_INTEGER) {\n    anonJobCounter = 0;\n  }\n  anonJobCounter++;\n  return `<Anonymous Job ${anonJobCounter} ${now.toISOString()}>`;\n}\nfunction Job(name, job, callback) {\n  // setup a private pendingInvocations variable\n  this.pendingInvocations = [];\n\n  //setup a private number of invocations variable\n  let triggeredJobs = 0;\n\n  // Set scope vars\n  const jobName = name && typeof name === 'string' ? name : resolveAnonJobName();\n  this.job = name && typeof name === 'function' ? name : job;\n\n  // Make sure callback is actually a callback\n  if (this.job === name) {\n    // Name wasn't provided and maybe a callback is there\n    this.callback = typeof job === 'function' ? job : false;\n  } else {\n    // Name was provided, and maybe a callback is there\n    this.callback = typeof callback === 'function' ? callback : false;\n  }\n\n  // task count\n  this.running = 0;\n\n  // Check for generator\n  if (typeof this.job === 'function' && this.job.prototype && this.job.prototype.next) {\n    this.job = function () {\n      return this.next().value;\n    }.bind(this.job.call(this));\n  }\n\n  // define properties\n  Object.defineProperty(this, 'name', {\n    value: jobName,\n    writable: false,\n    enumerable: true\n  });\n\n  // method that require private access\n  this.trackInvocation = function (invocation) {\n    // add to our invocation list\n    sorted.add(this.pendingInvocations, invocation, sorter);\n    return true;\n  };\n  this.stopTrackingInvocation = function (invocation) {\n    const invIdx = this.pendingInvocations.indexOf(invocation);\n    if (invIdx > -1) {\n      this.pendingInvocations.splice(invIdx, 1);\n      return true;\n    }\n    return false;\n  };\n  this.triggeredJobs = function () {\n    return triggeredJobs;\n  };\n  this.setTriggeredJobs = function (triggeredJob) {\n    triggeredJobs = triggeredJob;\n  };\n  this.deleteFromSchedule = function () {\n    deleteScheduledJob(this.name);\n  };\n  this.cancel = function (reschedule) {\n    reschedule = typeof reschedule == 'boolean' ? reschedule : false;\n    let inv, newInv;\n    const newInvs = [];\n    for (let j = 0; j < this.pendingInvocations.length; j++) {\n      inv = this.pendingInvocations[j];\n      cancelInvocation(inv);\n      if (reschedule && (inv.recurrenceRule.recurs || inv.recurrenceRule.next)) {\n        newInv = scheduleNextRecurrence(inv.recurrenceRule, this, inv.fireDate, inv.endDate);\n        if (newInv !== null) {\n          newInvs.push(newInv);\n        }\n      }\n    }\n    this.pendingInvocations = [];\n    for (let k = 0; k < newInvs.length; k++) {\n      this.trackInvocation(newInvs[k]);\n    }\n\n    // remove from scheduledJobs if reschedule === false\n    if (!reschedule) {\n      this.deleteFromSchedule();\n    }\n    return true;\n  };\n  this.cancelNext = function (reschedule) {\n    reschedule = typeof reschedule == 'boolean' ? reschedule : true;\n    if (!this.pendingInvocations.length) {\n      return false;\n    }\n    let newInv;\n    const nextInv = this.pendingInvocations.shift();\n    cancelInvocation(nextInv);\n    if (reschedule && (nextInv.recurrenceRule.recurs || nextInv.recurrenceRule.next)) {\n      newInv = scheduleNextRecurrence(nextInv.recurrenceRule, this, nextInv.fireDate, nextInv.endDate);\n      if (newInv !== null) {\n        this.trackInvocation(newInv);\n      }\n    }\n    return true;\n  };\n  this.reschedule = function (spec) {\n    let inv;\n    const invocationsToCancel = this.pendingInvocations.slice();\n    for (let j = 0; j < invocationsToCancel.length; j++) {\n      inv = invocationsToCancel[j];\n      cancelInvocation(inv);\n    }\n    this.pendingInvocations = [];\n    if (this.schedule(spec)) {\n      this.setTriggeredJobs(0);\n      return true;\n    } else {\n      this.pendingInvocations = invocationsToCancel;\n      return false;\n    }\n  };\n  this.nextInvocation = function () {\n    if (!this.pendingInvocations.length) {\n      return null;\n    }\n    return this.pendingInvocations[0].fireDate;\n  };\n}\nObject.setPrototypeOf(Job.prototype, events.EventEmitter.prototype);\nJob.prototype.invoke = function (fireDate) {\n  this.setTriggeredJobs(this.triggeredJobs() + 1);\n  return this.job(fireDate);\n};\nJob.prototype.runOnDate = function (date) {\n  return this.schedule(date);\n};\nJob.prototype.schedule = function (spec) {\n  const self = this;\n  let success = false;\n  let inv;\n  let start;\n  let end;\n  let tz;\n\n  // save passed-in value before 'spec' is replaced\n  if (typeof spec === 'object' && 'tz' in spec) {\n    tz = spec.tz;\n  }\n  if (typeof spec === 'object' && spec.rule) {\n    start = spec.start || undefined;\n    end = spec.end || undefined;\n    spec = spec.rule;\n    if (start) {\n      if (!(start instanceof Date)) {\n        start = new Date(start);\n      }\n      start = new CronDate(start, tz);\n      if (!isValidDate(start) || start.getTime() < Date.now()) {\n        start = undefined;\n      }\n    }\n    if (end && !(end instanceof Date) && !isValidDate(end = new Date(end))) {\n      end = undefined;\n    }\n    if (end) {\n      end = new CronDate(end, tz);\n    }\n  }\n  try {\n    const res = cronParser.parseExpression(spec, {\n      currentDate: start,\n      tz: tz\n    });\n    inv = scheduleNextRecurrence(res, self, start, end);\n    if (inv !== null) {\n      success = self.trackInvocation(inv);\n    }\n  } catch (err) {\n    const type = typeof spec;\n    if (type === 'string' || type === 'number') {\n      spec = new Date(spec);\n    }\n    if (spec instanceof Date && isValidDate(spec)) {\n      spec = new CronDate(spec);\n      self.isOneTimeJob = true;\n      if (spec.getTime() >= Date.now()) {\n        inv = new Invocation(self, spec);\n        scheduleInvocation(inv);\n        success = self.trackInvocation(inv);\n      }\n    } else if (type === 'object') {\n      self.isOneTimeJob = false;\n      if (!(spec instanceof RecurrenceRule)) {\n        const r = new RecurrenceRule();\n        if ('year' in spec) {\n          r.year = spec.year;\n        }\n        if ('month' in spec) {\n          r.month = spec.month;\n        }\n        if ('date' in spec) {\n          r.date = spec.date;\n        }\n        if ('dayOfWeek' in spec) {\n          r.dayOfWeek = spec.dayOfWeek;\n        }\n        if ('hour' in spec) {\n          r.hour = spec.hour;\n        }\n        if ('minute' in spec) {\n          r.minute = spec.minute;\n        }\n        if ('second' in spec) {\n          r.second = spec.second;\n        }\n        spec = r;\n      }\n      spec.tz = tz;\n      inv = scheduleNextRecurrence(spec, self, start, end);\n      if (inv !== null) {\n        success = self.trackInvocation(inv);\n      }\n    }\n  }\n  scheduledJobs[this.name] = this;\n  return success;\n};\nfunction deleteScheduledJob(name) {\n  if (name) {\n    delete scheduledJobs[name];\n  }\n}\nmodule.exports = {\n  Job,\n  deleteScheduledJob,\n  scheduledJobs\n};", "map": {"version": 3, "names": ["events", "require", "c<PERSON><PERSON><PERSON><PERSON>", "CronDate", "sorted", "scheduleNextRecurrence", "scheduleInvocation", "cancelInvocation", "RecurrenceRule", "sorter", "Invocation", "isValidDate", "scheduledJobs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resolveAnonJobName", "now", "Date", "Number", "MAX_SAFE_INTEGER", "toISOString", "Job", "name", "job", "callback", "pendingInvocations", "triggeredJobs", "job<PERSON>ame", "running", "prototype", "next", "value", "bind", "call", "Object", "defineProperty", "writable", "enumerable", "trackInvocation", "invocation", "add", "stopTrackingInvocation", "invIdx", "indexOf", "splice", "setTriggeredJobs", "<PERSON><PERSON><PERSON>", "deleteFromSchedule", "deleteScheduledJob", "cancel", "reschedule", "inv", "newInv", "newInvs", "j", "length", "recurrenceRule", "recurs", "fireDate", "endDate", "push", "k", "cancelNext", "nextInv", "shift", "spec", "invocationsToCancel", "slice", "schedule", "nextInvocation", "setPrototypeOf", "EventEmitter", "invoke", "runOnDate", "date", "self", "success", "start", "end", "tz", "rule", "undefined", "getTime", "res", "parseExpression", "currentDate", "err", "type", "isOneTimeJob", "r", "year", "month", "dayOfWeek", "hour", "minute", "second", "module", "exports"], "sources": ["C:/Users/<USER>/Documents/ekrani/node_modules/node-schedule/lib/Job.js"], "sourcesContent": ["'use strict';\n\nconst events = require('events')\nconst cronParser = require('cron-parser')\nconst CronDate = require('cron-parser/lib/date')\nconst sorted = require('sorted-array-functions')\n\nconst { scheduleNextRecurrence, scheduleInvocation, cancelInvocation, RecurrenceRule, sorter, Invocation } = require('./Invocation')\nconst { isValidDate } = require('./utils/dateUtils')\n\nconst scheduledJobs = {};\n\nlet anonJobCounter = 0;\nfunction resolveAnonJobName() {\n  const now = new Date()\n  if (anonJobCounter === Number.MAX_SAFE_INTEGER) {\n    anonJobCounter = 0\n  }\n  anonJobCounter++\n\n  return `<Anonymous Job ${anonJobCounter} ${now.toISOString()}>`\n}\n\nfunction Job(name, job, callback) {\n  // setup a private pendingInvocations variable\n  this.pendingInvocations = [];\n\n  //setup a private number of invocations variable\n  let triggeredJobs = 0;\n\n  // Set scope vars\n  const jobName = name && typeof name === 'string' ? name : resolveAnonJobName();\n  this.job = name && typeof name === 'function' ? name : job;\n\n  // Make sure callback is actually a callback\n  if (this.job === name) {\n    // Name wasn't provided and maybe a callback is there\n    this.callback = typeof job === 'function' ? job : false;\n  } else {\n    // Name was provided, and maybe a callback is there\n    this.callback = typeof callback === 'function' ? callback : false;\n  }\n\n  // task count\n  this.running = 0;\n\n  // Check for generator\n  if (typeof this.job === 'function' &&\n    this.job.prototype &&\n    this.job.prototype.next) {\n    this.job = function() {\n      return this.next().value;\n    }.bind(this.job.call(this));\n  }\n\n  // define properties\n  Object.defineProperty(this, 'name', {\n    value: jobName,\n    writable: false,\n    enumerable: true\n  });\n\n  // method that require private access\n  this.trackInvocation = function(invocation) {\n    // add to our invocation list\n    sorted.add(this.pendingInvocations, invocation, sorter);\n    return true;\n  };\n  this.stopTrackingInvocation = function(invocation) {\n    const invIdx = this.pendingInvocations.indexOf(invocation);\n    if (invIdx > -1) {\n      this.pendingInvocations.splice(invIdx, 1);\n      return true;\n    }\n\n    return false;\n  };\n  this.triggeredJobs = function() {\n    return triggeredJobs;\n  };\n  this.setTriggeredJobs = function(triggeredJob) {\n    triggeredJobs = triggeredJob;\n  };\n  this.deleteFromSchedule = function() {\n    deleteScheduledJob(this.name)\n  };\n  this.cancel = function(reschedule) {\n    reschedule = (typeof reschedule == 'boolean') ? reschedule : false;\n\n    let inv, newInv;\n    const newInvs = [];\n    for (let j = 0; j < this.pendingInvocations.length; j++) {\n      inv = this.pendingInvocations[j];\n\n      cancelInvocation(inv);\n\n      if (reschedule && (inv.recurrenceRule.recurs || inv.recurrenceRule.next)) {\n        newInv = scheduleNextRecurrence(inv.recurrenceRule, this, inv.fireDate, inv.endDate);\n        if (newInv !== null) {\n          newInvs.push(newInv);\n        }\n      }\n    }\n\n    this.pendingInvocations = [];\n\n    for (let k = 0; k < newInvs.length; k++) {\n      this.trackInvocation(newInvs[k]);\n    }\n\n    // remove from scheduledJobs if reschedule === false\n    if (!reschedule) {\n      this.deleteFromSchedule()\n    }\n\n    return true;\n  };\n  this.cancelNext = function(reschedule) {\n    reschedule = (typeof reschedule == 'boolean') ? reschedule : true;\n\n    if (!this.pendingInvocations.length) {\n      return false;\n    }\n\n    let newInv;\n    const nextInv = this.pendingInvocations.shift();\n\n    cancelInvocation(nextInv);\n\n    if (reschedule && (nextInv.recurrenceRule.recurs || nextInv.recurrenceRule.next)) {\n      newInv = scheduleNextRecurrence(nextInv.recurrenceRule, this, nextInv.fireDate, nextInv.endDate);\n      if (newInv !== null) {\n        this.trackInvocation(newInv);\n      }\n    }\n\n    return true;\n  };\n  this.reschedule = function(spec) {\n    let inv;\n    const invocationsToCancel = this.pendingInvocations.slice();\n\n    for (let j = 0; j < invocationsToCancel.length; j++) {\n      inv = invocationsToCancel[j];\n\n      cancelInvocation(inv);\n    }\n\n    this.pendingInvocations = [];\n\n    if (this.schedule(spec)) {\n      this.setTriggeredJobs(0);\n      return true;\n    } else {\n      this.pendingInvocations = invocationsToCancel;\n      return false;\n    }\n  };\n  this.nextInvocation = function() {\n    if (!this.pendingInvocations.length) {\n      return null;\n    }\n    return this.pendingInvocations[0].fireDate;\n  };\n}\n\nObject.setPrototypeOf(Job.prototype, events.EventEmitter.prototype);\n\nJob.prototype.invoke = function(fireDate) {\n  this.setTriggeredJobs(this.triggeredJobs() + 1);\n  return this.job(fireDate);\n};\n\nJob.prototype.runOnDate = function(date) {\n  return this.schedule(date);\n};\n\nJob.prototype.schedule = function(spec) {\n  const self = this;\n  let success = false;\n  let inv;\n  let start;\n  let end;\n  let tz;\n\n  // save passed-in value before 'spec' is replaced\n  if (typeof spec === 'object' && 'tz' in spec) {\n    tz = spec.tz;\n  }\n\n  if (typeof spec === 'object' && spec.rule) {\n    start = spec.start || undefined;\n    end = spec.end || undefined;\n    spec = spec.rule;\n\n    if (start) {\n      if (!(start instanceof Date)) {\n        start = new Date(start);\n      }\n\n      start = new CronDate(start, tz);\n      if (!isValidDate(start) || start.getTime() < Date.now()) {\n        start = undefined;\n      }\n    }\n\n    if (end && !(end instanceof Date) && !isValidDate(end = new Date(end))) {\n      end = undefined;\n    }\n\n    if (end) {\n      end = new CronDate(end, tz);\n    }\n  }\n\n  try {\n    const res = cronParser.parseExpression(spec, {currentDate: start, tz: tz});\n    inv = scheduleNextRecurrence(res, self, start, end);\n    if (inv !== null) {\n      success = self.trackInvocation(inv);\n    }\n  } catch (err) {\n    const type = typeof spec;\n    if ((type === 'string') || (type === 'number')) {\n      spec = new Date(spec);\n    }\n\n    if ((spec instanceof Date) && (isValidDate(spec))) {\n      spec = new CronDate(spec);\n      self.isOneTimeJob = true;\n      if (spec.getTime() >= Date.now()) {\n        inv = new Invocation(self, spec);\n        scheduleInvocation(inv);\n        success = self.trackInvocation(inv);\n      }\n    } else if (type === 'object') {\n      self.isOneTimeJob = false;\n      if (!(spec instanceof RecurrenceRule)) {\n        const r = new RecurrenceRule();\n        if ('year' in spec) {\n          r.year = spec.year;\n        }\n        if ('month' in spec) {\n          r.month = spec.month;\n        }\n        if ('date' in spec) {\n          r.date = spec.date;\n        }\n        if ('dayOfWeek' in spec) {\n          r.dayOfWeek = spec.dayOfWeek;\n        }\n        if ('hour' in spec) {\n          r.hour = spec.hour;\n        }\n        if ('minute' in spec) {\n          r.minute = spec.minute;\n        }\n        if ('second' in spec) {\n          r.second = spec.second;\n        }\n\n        spec = r;\n      }\n\n      spec.tz = tz;\n      inv = scheduleNextRecurrence(spec, self, start, end);\n      if (inv !== null) {\n        success = self.trackInvocation(inv);\n      }\n    }\n  }\n\n  scheduledJobs[this.name] = this;\n  return success;\n};\n\nfunction deleteScheduledJob(name) {\n  if (name) {\n    delete scheduledJobs[name];\n  }\n}\n\nmodule.exports = {\n  Job,\n  deleteScheduledJob,\n  scheduledJobs\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,MAAM,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAChC,MAAMC,UAAU,GAAGD,OAAO,CAAC,aAAa,CAAC;AACzC,MAAME,QAAQ,GAAGF,OAAO,CAAC,sBAAsB,CAAC;AAChD,MAAMG,MAAM,GAAGH,OAAO,CAAC,wBAAwB,CAAC;AAEhD,MAAM;EAAEI,sBAAsB;EAAEC,kBAAkB;EAAEC,gBAAgB;EAAEC,cAAc;EAAEC,MAAM;EAAEC;AAAW,CAAC,GAAGT,OAAO,CAAC,cAAc,CAAC;AACpI,MAAM;EAAEU;AAAY,CAAC,GAAGV,OAAO,CAAC,mBAAmB,CAAC;AAEpD,MAAMW,aAAa,GAAG,CAAC,CAAC;AAExB,IAAIC,cAAc,GAAG,CAAC;AACtB,SAASC,kBAAkBA,CAAA,EAAG;EAC5B,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;EACtB,IAAIH,cAAc,KAAKI,MAAM,CAACC,gBAAgB,EAAE;IAC9CL,cAAc,GAAG,CAAC;EACpB;EACAA,cAAc,EAAE;EAEhB,OAAO,kBAAkBA,cAAc,IAAIE,GAAG,CAACI,WAAW,CAAC,CAAC,GAAG;AACjE;AAEA,SAASC,GAAGA,CAACC,IAAI,EAAEC,GAAG,EAAEC,QAAQ,EAAE;EAChC;EACA,IAAI,CAACC,kBAAkB,GAAG,EAAE;;EAE5B;EACA,IAAIC,aAAa,GAAG,CAAC;;EAErB;EACA,MAAMC,OAAO,GAAGL,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGP,kBAAkB,CAAC,CAAC;EAC9E,IAAI,CAACQ,GAAG,GAAGD,IAAI,IAAI,OAAOA,IAAI,KAAK,UAAU,GAAGA,IAAI,GAAGC,GAAG;;EAE1D;EACA,IAAI,IAAI,CAACA,GAAG,KAAKD,IAAI,EAAE;IACrB;IACA,IAAI,CAACE,QAAQ,GAAG,OAAOD,GAAG,KAAK,UAAU,GAAGA,GAAG,GAAG,KAAK;EACzD,CAAC,MAAM;IACL;IACA,IAAI,CAACC,QAAQ,GAAG,OAAOA,QAAQ,KAAK,UAAU,GAAGA,QAAQ,GAAG,KAAK;EACnE;;EAEA;EACA,IAAI,CAACI,OAAO,GAAG,CAAC;;EAEhB;EACA,IAAI,OAAO,IAAI,CAACL,GAAG,KAAK,UAAU,IAChC,IAAI,CAACA,GAAG,CAACM,SAAS,IAClB,IAAI,CAACN,GAAG,CAACM,SAAS,CAACC,IAAI,EAAE;IACzB,IAAI,CAACP,GAAG,GAAG,YAAW;MACpB,OAAO,IAAI,CAACO,IAAI,CAAC,CAAC,CAACC,KAAK;IAC1B,CAAC,CAACC,IAAI,CAAC,IAAI,CAACT,GAAG,CAACU,IAAI,CAAC,IAAI,CAAC,CAAC;EAC7B;;EAEA;EACAC,MAAM,CAACC,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE;IAClCJ,KAAK,EAAEJ,OAAO;IACdS,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,IAAI,CAACC,eAAe,GAAG,UAASC,UAAU,EAAE;IAC1C;IACAlC,MAAM,CAACmC,GAAG,CAAC,IAAI,CAACf,kBAAkB,EAAEc,UAAU,EAAE7B,MAAM,CAAC;IACvD,OAAO,IAAI;EACb,CAAC;EACD,IAAI,CAAC+B,sBAAsB,GAAG,UAASF,UAAU,EAAE;IACjD,MAAMG,MAAM,GAAG,IAAI,CAACjB,kBAAkB,CAACkB,OAAO,CAACJ,UAAU,CAAC;IAC1D,IAAIG,MAAM,GAAG,CAAC,CAAC,EAAE;MACf,IAAI,CAACjB,kBAAkB,CAACmB,MAAM,CAACF,MAAM,EAAE,CAAC,CAAC;MACzC,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd,CAAC;EACD,IAAI,CAAChB,aAAa,GAAG,YAAW;IAC9B,OAAOA,aAAa;EACtB,CAAC;EACD,IAAI,CAACmB,gBAAgB,GAAG,UAASC,YAAY,EAAE;IAC7CpB,aAAa,GAAGoB,YAAY;EAC9B,CAAC;EACD,IAAI,CAACC,kBAAkB,GAAG,YAAW;IACnCC,kBAAkB,CAAC,IAAI,CAAC1B,IAAI,CAAC;EAC/B,CAAC;EACD,IAAI,CAAC2B,MAAM,GAAG,UAASC,UAAU,EAAE;IACjCA,UAAU,GAAI,OAAOA,UAAU,IAAI,SAAS,GAAIA,UAAU,GAAG,KAAK;IAElE,IAAIC,GAAG,EAAEC,MAAM;IACf,MAAMC,OAAO,GAAG,EAAE;IAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC7B,kBAAkB,CAAC8B,MAAM,EAAED,CAAC,EAAE,EAAE;MACvDH,GAAG,GAAG,IAAI,CAAC1B,kBAAkB,CAAC6B,CAAC,CAAC;MAEhC9C,gBAAgB,CAAC2C,GAAG,CAAC;MAErB,IAAID,UAAU,KAAKC,GAAG,CAACK,cAAc,CAACC,MAAM,IAAIN,GAAG,CAACK,cAAc,CAAC1B,IAAI,CAAC,EAAE;QACxEsB,MAAM,GAAG9C,sBAAsB,CAAC6C,GAAG,CAACK,cAAc,EAAE,IAAI,EAAEL,GAAG,CAACO,QAAQ,EAAEP,GAAG,CAACQ,OAAO,CAAC;QACpF,IAAIP,MAAM,KAAK,IAAI,EAAE;UACnBC,OAAO,CAACO,IAAI,CAACR,MAAM,CAAC;QACtB;MACF;IACF;IAEA,IAAI,CAAC3B,kBAAkB,GAAG,EAAE;IAE5B,KAAK,IAAIoC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,OAAO,CAACE,MAAM,EAAEM,CAAC,EAAE,EAAE;MACvC,IAAI,CAACvB,eAAe,CAACe,OAAO,CAACQ,CAAC,CAAC,CAAC;IAClC;;IAEA;IACA,IAAI,CAACX,UAAU,EAAE;MACf,IAAI,CAACH,kBAAkB,CAAC,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb,CAAC;EACD,IAAI,CAACe,UAAU,GAAG,UAASZ,UAAU,EAAE;IACrCA,UAAU,GAAI,OAAOA,UAAU,IAAI,SAAS,GAAIA,UAAU,GAAG,IAAI;IAEjE,IAAI,CAAC,IAAI,CAACzB,kBAAkB,CAAC8B,MAAM,EAAE;MACnC,OAAO,KAAK;IACd;IAEA,IAAIH,MAAM;IACV,MAAMW,OAAO,GAAG,IAAI,CAACtC,kBAAkB,CAACuC,KAAK,CAAC,CAAC;IAE/CxD,gBAAgB,CAACuD,OAAO,CAAC;IAEzB,IAAIb,UAAU,KAAKa,OAAO,CAACP,cAAc,CAACC,MAAM,IAAIM,OAAO,CAACP,cAAc,CAAC1B,IAAI,CAAC,EAAE;MAChFsB,MAAM,GAAG9C,sBAAsB,CAACyD,OAAO,CAACP,cAAc,EAAE,IAAI,EAAEO,OAAO,CAACL,QAAQ,EAAEK,OAAO,CAACJ,OAAO,CAAC;MAChG,IAAIP,MAAM,KAAK,IAAI,EAAE;QACnB,IAAI,CAACd,eAAe,CAACc,MAAM,CAAC;MAC9B;IACF;IAEA,OAAO,IAAI;EACb,CAAC;EACD,IAAI,CAACF,UAAU,GAAG,UAASe,IAAI,EAAE;IAC/B,IAAId,GAAG;IACP,MAAMe,mBAAmB,GAAG,IAAI,CAACzC,kBAAkB,CAAC0C,KAAK,CAAC,CAAC;IAE3D,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,mBAAmB,CAACX,MAAM,EAAED,CAAC,EAAE,EAAE;MACnDH,GAAG,GAAGe,mBAAmB,CAACZ,CAAC,CAAC;MAE5B9C,gBAAgB,CAAC2C,GAAG,CAAC;IACvB;IAEA,IAAI,CAAC1B,kBAAkB,GAAG,EAAE;IAE5B,IAAI,IAAI,CAAC2C,QAAQ,CAACH,IAAI,CAAC,EAAE;MACvB,IAAI,CAACpB,gBAAgB,CAAC,CAAC,CAAC;MACxB,OAAO,IAAI;IACb,CAAC,MAAM;MACL,IAAI,CAACpB,kBAAkB,GAAGyC,mBAAmB;MAC7C,OAAO,KAAK;IACd;EACF,CAAC;EACD,IAAI,CAACG,cAAc,GAAG,YAAW;IAC/B,IAAI,CAAC,IAAI,CAAC5C,kBAAkB,CAAC8B,MAAM,EAAE;MACnC,OAAO,IAAI;IACb;IACA,OAAO,IAAI,CAAC9B,kBAAkB,CAAC,CAAC,CAAC,CAACiC,QAAQ;EAC5C,CAAC;AACH;AAEAxB,MAAM,CAACoC,cAAc,CAACjD,GAAG,CAACQ,SAAS,EAAE5B,MAAM,CAACsE,YAAY,CAAC1C,SAAS,CAAC;AAEnER,GAAG,CAACQ,SAAS,CAAC2C,MAAM,GAAG,UAASd,QAAQ,EAAE;EACxC,IAAI,CAACb,gBAAgB,CAAC,IAAI,CAACnB,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC;EAC/C,OAAO,IAAI,CAACH,GAAG,CAACmC,QAAQ,CAAC;AAC3B,CAAC;AAEDrC,GAAG,CAACQ,SAAS,CAAC4C,SAAS,GAAG,UAASC,IAAI,EAAE;EACvC,OAAO,IAAI,CAACN,QAAQ,CAACM,IAAI,CAAC;AAC5B,CAAC;AAEDrD,GAAG,CAACQ,SAAS,CAACuC,QAAQ,GAAG,UAASH,IAAI,EAAE;EACtC,MAAMU,IAAI,GAAG,IAAI;EACjB,IAAIC,OAAO,GAAG,KAAK;EACnB,IAAIzB,GAAG;EACP,IAAI0B,KAAK;EACT,IAAIC,GAAG;EACP,IAAIC,EAAE;;EAEN;EACA,IAAI,OAAOd,IAAI,KAAK,QAAQ,IAAI,IAAI,IAAIA,IAAI,EAAE;IAC5Cc,EAAE,GAAGd,IAAI,CAACc,EAAE;EACd;EAEA,IAAI,OAAOd,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACe,IAAI,EAAE;IACzCH,KAAK,GAAGZ,IAAI,CAACY,KAAK,IAAII,SAAS;IAC/BH,GAAG,GAAGb,IAAI,CAACa,GAAG,IAAIG,SAAS;IAC3BhB,IAAI,GAAGA,IAAI,CAACe,IAAI;IAEhB,IAAIH,KAAK,EAAE;MACT,IAAI,EAAEA,KAAK,YAAY5D,IAAI,CAAC,EAAE;QAC5B4D,KAAK,GAAG,IAAI5D,IAAI,CAAC4D,KAAK,CAAC;MACzB;MAEAA,KAAK,GAAG,IAAIzE,QAAQ,CAACyE,KAAK,EAAEE,EAAE,CAAC;MAC/B,IAAI,CAACnE,WAAW,CAACiE,KAAK,CAAC,IAAIA,KAAK,CAACK,OAAO,CAAC,CAAC,GAAGjE,IAAI,CAACD,GAAG,CAAC,CAAC,EAAE;QACvD6D,KAAK,GAAGI,SAAS;MACnB;IACF;IAEA,IAAIH,GAAG,IAAI,EAAEA,GAAG,YAAY7D,IAAI,CAAC,IAAI,CAACL,WAAW,CAACkE,GAAG,GAAG,IAAI7D,IAAI,CAAC6D,GAAG,CAAC,CAAC,EAAE;MACtEA,GAAG,GAAGG,SAAS;IACjB;IAEA,IAAIH,GAAG,EAAE;MACPA,GAAG,GAAG,IAAI1E,QAAQ,CAAC0E,GAAG,EAAEC,EAAE,CAAC;IAC7B;EACF;EAEA,IAAI;IACF,MAAMI,GAAG,GAAGhF,UAAU,CAACiF,eAAe,CAACnB,IAAI,EAAE;MAACoB,WAAW,EAAER,KAAK;MAAEE,EAAE,EAAEA;IAAE,CAAC,CAAC;IAC1E5B,GAAG,GAAG7C,sBAAsB,CAAC6E,GAAG,EAAER,IAAI,EAAEE,KAAK,EAAEC,GAAG,CAAC;IACnD,IAAI3B,GAAG,KAAK,IAAI,EAAE;MAChByB,OAAO,GAAGD,IAAI,CAACrC,eAAe,CAACa,GAAG,CAAC;IACrC;EACF,CAAC,CAAC,OAAOmC,GAAG,EAAE;IACZ,MAAMC,IAAI,GAAG,OAAOtB,IAAI;IACxB,IAAKsB,IAAI,KAAK,QAAQ,IAAMA,IAAI,KAAK,QAAS,EAAE;MAC9CtB,IAAI,GAAG,IAAIhD,IAAI,CAACgD,IAAI,CAAC;IACvB;IAEA,IAAKA,IAAI,YAAYhD,IAAI,IAAML,WAAW,CAACqD,IAAI,CAAE,EAAE;MACjDA,IAAI,GAAG,IAAI7D,QAAQ,CAAC6D,IAAI,CAAC;MACzBU,IAAI,CAACa,YAAY,GAAG,IAAI;MACxB,IAAIvB,IAAI,CAACiB,OAAO,CAAC,CAAC,IAAIjE,IAAI,CAACD,GAAG,CAAC,CAAC,EAAE;QAChCmC,GAAG,GAAG,IAAIxC,UAAU,CAACgE,IAAI,EAAEV,IAAI,CAAC;QAChC1D,kBAAkB,CAAC4C,GAAG,CAAC;QACvByB,OAAO,GAAGD,IAAI,CAACrC,eAAe,CAACa,GAAG,CAAC;MACrC;IACF,CAAC,MAAM,IAAIoC,IAAI,KAAK,QAAQ,EAAE;MAC5BZ,IAAI,CAACa,YAAY,GAAG,KAAK;MACzB,IAAI,EAAEvB,IAAI,YAAYxD,cAAc,CAAC,EAAE;QACrC,MAAMgF,CAAC,GAAG,IAAIhF,cAAc,CAAC,CAAC;QAC9B,IAAI,MAAM,IAAIwD,IAAI,EAAE;UAClBwB,CAAC,CAACC,IAAI,GAAGzB,IAAI,CAACyB,IAAI;QACpB;QACA,IAAI,OAAO,IAAIzB,IAAI,EAAE;UACnBwB,CAAC,CAACE,KAAK,GAAG1B,IAAI,CAAC0B,KAAK;QACtB;QACA,IAAI,MAAM,IAAI1B,IAAI,EAAE;UAClBwB,CAAC,CAACf,IAAI,GAAGT,IAAI,CAACS,IAAI;QACpB;QACA,IAAI,WAAW,IAAIT,IAAI,EAAE;UACvBwB,CAAC,CAACG,SAAS,GAAG3B,IAAI,CAAC2B,SAAS;QAC9B;QACA,IAAI,MAAM,IAAI3B,IAAI,EAAE;UAClBwB,CAAC,CAACI,IAAI,GAAG5B,IAAI,CAAC4B,IAAI;QACpB;QACA,IAAI,QAAQ,IAAI5B,IAAI,EAAE;UACpBwB,CAAC,CAACK,MAAM,GAAG7B,IAAI,CAAC6B,MAAM;QACxB;QACA,IAAI,QAAQ,IAAI7B,IAAI,EAAE;UACpBwB,CAAC,CAACM,MAAM,GAAG9B,IAAI,CAAC8B,MAAM;QACxB;QAEA9B,IAAI,GAAGwB,CAAC;MACV;MAEAxB,IAAI,CAACc,EAAE,GAAGA,EAAE;MACZ5B,GAAG,GAAG7C,sBAAsB,CAAC2D,IAAI,EAAEU,IAAI,EAAEE,KAAK,EAAEC,GAAG,CAAC;MACpD,IAAI3B,GAAG,KAAK,IAAI,EAAE;QAChByB,OAAO,GAAGD,IAAI,CAACrC,eAAe,CAACa,GAAG,CAAC;MACrC;IACF;EACF;EAEAtC,aAAa,CAAC,IAAI,CAACS,IAAI,CAAC,GAAG,IAAI;EAC/B,OAAOsD,OAAO;AAChB,CAAC;AAED,SAAS5B,kBAAkBA,CAAC1B,IAAI,EAAE;EAChC,IAAIA,IAAI,EAAE;IACR,OAAOT,aAAa,CAACS,IAAI,CAAC;EAC5B;AACF;AAEA0E,MAAM,CAACC,OAAO,GAAG;EACf5E,GAAG;EACH2B,kBAAkB;EAClBnC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}