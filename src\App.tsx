import { BrowserRouter as Router, Route, Routes } from "react-router-dom";
import FormPage from "./pages/register/FormPage";
import SettingsPage from "./pages/settings/SettingsPage";
import { HomePage } from "./pages/home/<USER>";

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/form" Component={FormPage} />
        <Route path="/" Component={HomePage} />
        <Route path="/settings" Component={SettingsPage} />
      </Routes>
    </Router>
  );
}

export default App;
