{"ast": null, "code": "'use strict';\n\n/*\n  node-schedule\n  A cron-like and not-cron-like job scheduler for Node.\n*/\nconst {\n  Job,\n  scheduledJobs\n} = require('./Job');\n\n/* API\n  invoke()\n  runOnDate(date)\n  schedule(date || recurrenceRule || cronstring)\n  cancel(reschedule = false)\n  cancelNext(reschedule = true)\n\n   Property constraints\n  name: readonly\n  job: readwrite\n*/\n\n/* Convenience methods */\nfunction scheduleJob() {\n  if (arguments.length < 2) {\n    throw new RangeError('Invalid number of arguments');\n  }\n  const name = arguments.length >= 3 && typeof arguments[0] === 'string' ? arguments[0] : null;\n  const spec = name ? arguments[1] : arguments[0];\n  const method = name ? arguments[2] : arguments[1];\n  const callback = name ? arguments[3] : arguments[2];\n  if (typeof method !== 'function') {\n    throw new RangeError('The job method must be a function.');\n  }\n  const job = new Job(name, method, callback);\n  if (job.schedule(spec)) {\n    return job;\n  }\n  return null;\n}\nfunction rescheduleJob(job, spec) {\n  if (job instanceof Job) {\n    if (job.reschedule(spec)) {\n      return job;\n    }\n  } else if (typeof job === 'string') {\n    if (Object.prototype.hasOwnProperty.call(scheduledJobs, job)) {\n      if (scheduledJobs[job].reschedule(spec)) {\n        return scheduledJobs[job];\n      }\n    } else {\n      throw new Error('Cannot reschedule one-off job by name, pass job reference instead');\n    }\n  }\n  return null;\n}\nfunction cancelJob(job) {\n  let success = false;\n  if (job instanceof Job) {\n    success = job.cancel();\n  } else if (typeof job == 'string' || job instanceof String) {\n    if (job in scheduledJobs && Object.prototype.hasOwnProperty.call(scheduledJobs, job)) {\n      success = scheduledJobs[job].cancel();\n    }\n  }\n  return success;\n}\nfunction gracefulShutdown() {\n  const jobs = Object.keys(scheduledJobs).map(key => scheduledJobs[key]);\n  jobs.forEach(function (job) {\n    job.cancel();\n  });\n  let running = false;\n  for (let i = 0; i < jobs.length; i++) {\n    if (jobs[i].running > 0) {\n      running = true;\n      break;\n    }\n  }\n  return new Promise(function (resolve) {\n    if (running) {\n      setInterval(function () {\n        for (let i = 0; i < jobs.length; i++) {\n          if (jobs[i].running > 0) {\n            return;\n          }\n        }\n        resolve();\n      }, 500);\n    } else {\n      resolve();\n    }\n  });\n}\n\n/* Public API */\nmodule.exports = {\n  scheduleJob,\n  rescheduleJob,\n  scheduledJobs,\n  cancelJob,\n  gracefulShutdown\n};", "map": {"version": 3, "names": ["Job", "scheduledJobs", "require", "scheduleJob", "arguments", "length", "RangeError", "name", "spec", "method", "callback", "job", "schedule", "reschedule<PERSON>ob", "reschedule", "Object", "prototype", "hasOwnProperty", "call", "Error", "cancelJob", "success", "cancel", "String", "gracefulShutdown", "jobs", "keys", "map", "key", "for<PERSON>ach", "running", "i", "Promise", "resolve", "setInterval", "module", "exports"], "sources": ["C:/Users/<USER>/Documents/ekrani/node_modules/node-schedule/lib/schedule.js"], "sourcesContent": ["'use strict';\n\n/*\n  node-schedule\n  A cron-like and not-cron-like job scheduler for Node.\n*/\n\nconst { Job, scheduledJobs } = require('./Job')\n\n/* API\n  invoke()\n  runOnDate(date)\n  schedule(date || recurrenceRule || cronstring)\n  cancel(reschedule = false)\n  cancelNext(reschedule = true)\n\n   Property constraints\n  name: readonly\n  job: readwrite\n*/\n\n/* Convenience methods */\nfunction scheduleJob() {\n  if (arguments.length < 2) {\n    throw new RangeError('Invalid number of arguments');\n  }\n\n  const name = (arguments.length >= 3 && typeof arguments[0] === 'string') ? arguments[0] : null;\n  const spec = name ? arguments[1] : arguments[0];\n  const method = name ? arguments[2] : arguments[1];\n  const callback = name ? arguments[3] : arguments[2];\n\n  if (typeof method !== 'function') {\n    throw new RangeError('The job method must be a function.');\n  }\n\n  const job = new Job(name, method, callback);\n\n  if (job.schedule(spec)) {\n    return job;\n  }\n\n  return null;\n}\n\nfunction rescheduleJob(job, spec) {\n  if (job instanceof Job) {\n    if (job.reschedule(spec)) {\n      return job;\n    }\n  } else if (typeof job === 'string') {\n    if (Object.prototype.hasOwnProperty.call(scheduledJobs, job)) {\n      if (scheduledJobs[job].reschedule(spec)) {\n        return scheduledJobs[job];\n      }\n    } else {\n      throw new Error('Cannot reschedule one-off job by name, pass job reference instead')\n    }\n  }\n  return null;\n}\n\nfunction cancelJob(job) {\n  let success = false;\n  if (job instanceof Job) {\n    success = job.cancel();\n  } else if (typeof job == 'string' || job instanceof String) {\n    if (job in scheduledJobs && Object.prototype.hasOwnProperty.call(scheduledJobs, job)) {\n      success = scheduledJobs[job].cancel();\n    }\n  }\n\n  return success;\n}\n\nfunction gracefulShutdown() {\n  const jobs = Object.keys(scheduledJobs).map(key => scheduledJobs[key]);\n  jobs.forEach(function (job) {\n    job.cancel();\n  });\n\n  let running = false;\n  for (let i = 0; i < jobs.length; i++) {\n    if (jobs[i].running > 0) {\n      running = true;\n      break;\n    }\n  }\n\n  return new Promise(function (resolve) {\n    if (running) {\n      setInterval(function () {\n        for (let i = 0; i < jobs.length; i++) {\n          if (jobs[i].running > 0) {\n            return;\n          }\n        }\n\n        resolve();\n      }, 500);\n    } else {\n      resolve();\n    }\n  });\n}\n\n/* Public API */\nmodule.exports = {\n  scheduleJob,\n  rescheduleJob,\n  scheduledJobs,\n  cancelJob,\n  gracefulShutdown,\n}\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AAEA,MAAM;EAAEA,GAAG;EAAEC;AAAc,CAAC,GAAGC,OAAO,CAAC,OAAO,CAAC;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASC,WAAWA,CAAA,EAAG;EACrB,IAAIC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;IACxB,MAAM,IAAIC,UAAU,CAAC,6BAA6B,CAAC;EACrD;EAEA,MAAMC,IAAI,GAAIH,SAAS,CAACC,MAAM,IAAI,CAAC,IAAI,OAAOD,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,GAAIA,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EAC9F,MAAMI,IAAI,GAAGD,IAAI,GAAGH,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC;EAC/C,MAAMK,MAAM,GAAGF,IAAI,GAAGH,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC;EACjD,MAAMM,QAAQ,GAAGH,IAAI,GAAGH,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC;EAEnD,IAAI,OAAOK,MAAM,KAAK,UAAU,EAAE;IAChC,MAAM,IAAIH,UAAU,CAAC,oCAAoC,CAAC;EAC5D;EAEA,MAAMK,GAAG,GAAG,IAAIX,GAAG,CAACO,IAAI,EAAEE,MAAM,EAAEC,QAAQ,CAAC;EAE3C,IAAIC,GAAG,CAACC,QAAQ,CAACJ,IAAI,CAAC,EAAE;IACtB,OAAOG,GAAG;EACZ;EAEA,OAAO,IAAI;AACb;AAEA,SAASE,aAAaA,CAACF,GAAG,EAAEH,IAAI,EAAE;EAChC,IAAIG,GAAG,YAAYX,GAAG,EAAE;IACtB,IAAIW,GAAG,CAACG,UAAU,CAACN,IAAI,CAAC,EAAE;MACxB,OAAOG,GAAG;IACZ;EACF,CAAC,MAAM,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAClC,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACjB,aAAa,EAAEU,GAAG,CAAC,EAAE;MAC5D,IAAIV,aAAa,CAACU,GAAG,CAAC,CAACG,UAAU,CAACN,IAAI,CAAC,EAAE;QACvC,OAAOP,aAAa,CAACU,GAAG,CAAC;MAC3B;IACF,CAAC,MAAM;MACL,MAAM,IAAIQ,KAAK,CAAC,mEAAmE,CAAC;IACtF;EACF;EACA,OAAO,IAAI;AACb;AAEA,SAASC,SAASA,CAACT,GAAG,EAAE;EACtB,IAAIU,OAAO,GAAG,KAAK;EACnB,IAAIV,GAAG,YAAYX,GAAG,EAAE;IACtBqB,OAAO,GAAGV,GAAG,CAACW,MAAM,CAAC,CAAC;EACxB,CAAC,MAAM,IAAI,OAAOX,GAAG,IAAI,QAAQ,IAAIA,GAAG,YAAYY,MAAM,EAAE;IAC1D,IAAIZ,GAAG,IAAIV,aAAa,IAAIc,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACjB,aAAa,EAAEU,GAAG,CAAC,EAAE;MACpFU,OAAO,GAAGpB,aAAa,CAACU,GAAG,CAAC,CAACW,MAAM,CAAC,CAAC;IACvC;EACF;EAEA,OAAOD,OAAO;AAChB;AAEA,SAASG,gBAAgBA,CAAA,EAAG;EAC1B,MAAMC,IAAI,GAAGV,MAAM,CAACW,IAAI,CAACzB,aAAa,CAAC,CAAC0B,GAAG,CAACC,GAAG,IAAI3B,aAAa,CAAC2B,GAAG,CAAC,CAAC;EACtEH,IAAI,CAACI,OAAO,CAAC,UAAUlB,GAAG,EAAE;IAC1BA,GAAG,CAACW,MAAM,CAAC,CAAC;EACd,CAAC,CAAC;EAEF,IAAIQ,OAAO,GAAG,KAAK;EACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,IAAI,CAACpB,MAAM,EAAE0B,CAAC,EAAE,EAAE;IACpC,IAAIN,IAAI,CAACM,CAAC,CAAC,CAACD,OAAO,GAAG,CAAC,EAAE;MACvBA,OAAO,GAAG,IAAI;MACd;IACF;EACF;EAEA,OAAO,IAAIE,OAAO,CAAC,UAAUC,OAAO,EAAE;IACpC,IAAIH,OAAO,EAAE;MACXI,WAAW,CAAC,YAAY;QACtB,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,IAAI,CAACpB,MAAM,EAAE0B,CAAC,EAAE,EAAE;UACpC,IAAIN,IAAI,CAACM,CAAC,CAAC,CAACD,OAAO,GAAG,CAAC,EAAE;YACvB;UACF;QACF;QAEAG,OAAO,CAAC,CAAC;MACX,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,MAAM;MACLA,OAAO,CAAC,CAAC;IACX;EACF,CAAC,CAAC;AACJ;;AAEA;AACAE,MAAM,CAACC,OAAO,GAAG;EACfjC,WAAW;EACXU,aAAa;EACbZ,aAAa;EACbmB,SAAS;EACTI;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}