.iqamah-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  padding: 20px;
  margin: 15px 0;
  transition: all 0.3s ease;
}

.iqamah-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.iqamah-time {
  font-size: 1.1rem;
  color: #333;
  margin-bottom: 15px;
  line-height: 1.4;
}

.placeholder {
  color: #007bbf;
  font-weight: 700;
}

.number-input-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.number-input-btn {
  width: 35px;
  height: 35px;
  border: 2px solid #007bbf;
  background: white;
  color: #007bbf;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.2rem;
  font-weight: bold;
  transition: all 0.3s ease;
}

.number-input-btn:hover {
  background: #007bbf;
  color: white;
}

.number-input-display {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  min-width: 40px;
  text-align: center;
}

.checkbox-container {
  display: flex;
  align-items: center;
}

.checkbox-label {
  display: flex;
  align-items: center;
  font-size: 1rem;
  color: #333;
  cursor: pointer;
}

.checkbox-show-in-tv {
  margin-right: 8px;
  transform: scale(1.2);
  accent-color: #007bbf;
}

@media (max-width: 768px) {
  .iqamah-card {
    padding: 15px;
  }
  
  .iqamah-time {
    font-size: 1rem;
  }
  
  .number-input-btn {
    width: 30px;
    height: 30px;
    font-size: 1rem;
  }
}
