import { useNavigate } from "react-router-dom";
import {  FormDataStorage } from "../register/FormPage";
import { useEffect, useState } from "react";
import "./settings-style.css";
import { Link } from "react-router-dom";

const SettingsPage = () => {
  const [titleAttentionText, setTitleAttentionText] = useState("");
  const [attentionDescriptionText, setAttentionDescriptionText] = useState("");

  const navigate = useNavigate();

  useEffect(() => {
    const formData: FormDataStorage | null = JSON.parse(
      localStorage.getItem("formData") || "null"
    );

    if (formData) {
      setTitleAttentionText(formData.attentionText?.title || "");
      setAttentionDescriptionText(formData.attentionText?.description || "");
    }
  }, []);

  const handleSave = () => {
    const formData: FormDataStorage | null = JSON.parse(
      localStorage.getItem("formData") || "null"
    );
    if (formData) {
      formData.attentionText.title = titleAttentionText;
      formData.attentionText.description = attentionDescriptionText;
      localStorage.setItem("formData", JSON.stringify(formData));
    }

    navigate("/");
  };

  const handleCancel = () => {
    const formData: FormDataStorage | null = JSON.parse(
      localStorage.getItem("formData") || "null"
    );
    setAttentionDescriptionText("");
    setAttentionDescriptionText("");
    if (formData) {
      formData.attentionText.title = "";
      formData.attentionText.description = "";
      localStorage.setItem("formData", JSON.stringify(formData));
    }
    navigate("/");
  };

  return (
    <div
      className="settings-page"
      style={{
        backgroundImage: `url(${require("../../assets/images/background-opacity.png")}), url(${require(`../../assets/images/wallpaper/${new Date().getDate()}.jpeg`)})`,
      }}
    >
      <div className="card">
        <h2 className="card-title">Shkruaj Lajmin Aktual</h2>
        <div className="card-content">
          <label htmlFor="attentionText" className="input-label">
            Titulli i lajmit:
          </label>
          <textarea
            id="attentionText"
            value={titleAttentionText}
            rows={2}
            onChange={(e) => setTitleAttentionText(e.target.value)}
            className="input-title"
          />
          <label htmlFor="attentionText" className="input-label">
            Përmbajtja e lajmit:
          </label>
          <textarea
            id="attentionText"
            value={attentionDescriptionText}
            onChange={(e) => setAttentionDescriptionText(e.target.value)}
            className="input-textarea"
          />
        </div>
        <button onClick={handleSave} className="save-button">
          Paraqit lajmin
        </button>
        <button onClick={handleCancel} className="cancel-button">
          Fshije nga paraqitja
        </button>
        <Link to="/form" style={{ marginTop: 20 }}>
          Ndrysho Preferencat
        </Link>
      </div>
    </div>
  );
};
export default SettingsPage;
