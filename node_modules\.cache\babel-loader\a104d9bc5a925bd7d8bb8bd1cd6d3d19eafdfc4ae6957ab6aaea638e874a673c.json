{"ast": null, "code": "'use strict';\n\nconst lt = require('long-timeout');\nconst CronDate = require('cron-parser/lib/date');\nconst sorted = require('sorted-array-functions');\nconst invocations = [];\nlet currentInvocation = null;\n\n/* DoesntRecur rule */\nconst DoesntRecur = new RecurrenceRule();\nDoesntRecur.recurs = false;\n\n/* Invocation object */\nfunction Invocation(job, fireDate, recurrenceRule, endDate) {\n  this.job = job;\n  this.fireDate = fireDate;\n  this.endDate = endDate;\n  this.recurrenceRule = recurrenceRule || DoesntRecur;\n  this.timerID = null;\n}\nfunction sorter(a, b) {\n  return a.fireDate.getTime() - b.fireDate.getTime();\n}\n\n/* Range object */\nfunction Range(start, end, step) {\n  this.start = start || 0;\n  this.end = end || 60;\n  this.step = step || 1;\n}\nRange.prototype.contains = function (val) {\n  if (this.step === null || this.step === 1) {\n    return val >= this.start && val <= this.end;\n  } else {\n    for (let i = this.start; i < this.end; i += this.step) {\n      if (i === val) {\n        return true;\n      }\n    }\n    return false;\n  }\n};\n\n/* RecurrenceRule object */\n/*\n  Interpreting each property:\n  null - any value is valid\n  number - fixed value\n  Range - value must fall in range\n  array - value must validate against any item in list\n\n  NOTE: Cron months are 1-based, but RecurrenceRule months are 0-based.\n*/\nfunction RecurrenceRule(year, month, date, dayOfWeek, hour, minute, second) {\n  this.recurs = true;\n  this.year = year == null ? null : year;\n  this.month = month == null ? null : month;\n  this.date = date == null ? null : date;\n  this.dayOfWeek = dayOfWeek == null ? null : dayOfWeek;\n  this.hour = hour == null ? null : hour;\n  this.minute = minute == null ? null : minute;\n  this.second = second == null ? 0 : second;\n}\nRecurrenceRule.prototype.isValid = function () {\n  function isValidType(num) {\n    if (Array.isArray(num) || num instanceof Array) {\n      return num.every(function (e) {\n        return isValidType(e);\n      });\n    }\n    return !(Number.isNaN(Number(num)) && !(num instanceof Range));\n  }\n  if (this.month !== null && (this.month < 0 || this.month > 11 || !isValidType(this.month))) {\n    return false;\n  }\n  if (this.dayOfWeek !== null && (this.dayOfWeek < 0 || this.dayOfWeek > 6 || !isValidType(this.dayOfWeek))) {\n    return false;\n  }\n  if (this.hour !== null && (this.hour < 0 || this.hour > 23 || !isValidType(this.hour))) {\n    return false;\n  }\n  if (this.minute !== null && (this.minute < 0 || this.minute > 59 || !isValidType(this.minute))) {\n    return false;\n  }\n  if (this.second !== null && (this.second < 0 || this.second > 59 || !isValidType(this.second))) {\n    return false;\n  }\n  if (this.date !== null) {\n    if (!isValidType(this.date)) {\n      return false;\n    }\n    switch (this.month) {\n      case 3:\n      case 5:\n      case 8:\n      case 10:\n        if (this.date < 1 || this.date > 30) {\n          return false;\n        }\n        break;\n      case 1:\n        if (this.date < 1 || this.date > 29) {\n          return false;\n        }\n        break;\n      default:\n        if (this.date < 1 || this.date > 31) {\n          return false;\n        }\n    }\n  }\n  return true;\n};\nRecurrenceRule.prototype.nextInvocationDate = function (base) {\n  const next = this._nextInvocationDate(base);\n  return next ? next.toDate() : null;\n};\nRecurrenceRule.prototype._nextInvocationDate = function (base) {\n  base = base instanceof CronDate || base instanceof Date ? base : new Date();\n  if (!this.recurs) {\n    return null;\n  }\n  if (!this.isValid()) {\n    return null;\n  }\n  const now = new CronDate(Date.now(), this.tz);\n  let fullYear = now.getFullYear();\n  if (this.year !== null && typeof this.year == 'number' && this.year < fullYear) {\n    return null;\n  }\n  let next = new CronDate(base.getTime(), this.tz);\n  next.addSecond();\n  while (true) {\n    if (this.year !== null) {\n      fullYear = next.getFullYear();\n      if (typeof this.year == 'number' && this.year < fullYear) {\n        next = null;\n        break;\n      }\n      if (!recurMatch(fullYear, this.year)) {\n        next.addYear();\n        next.setMonth(0);\n        next.setDate(1);\n        next.setHours(0);\n        next.setMinutes(0);\n        next.setSeconds(0);\n        continue;\n      }\n    }\n    if (this.month != null && !recurMatch(next.getMonth(), this.month)) {\n      next.addMonth();\n      continue;\n    }\n    if (this.date != null && !recurMatch(next.getDate(), this.date)) {\n      next.addDay();\n      continue;\n    }\n    if (this.dayOfWeek != null && !recurMatch(next.getDay(), this.dayOfWeek)) {\n      next.addDay();\n      continue;\n    }\n    if (this.hour != null && !recurMatch(next.getHours(), this.hour)) {\n      next.addHour();\n      continue;\n    }\n    if (this.minute != null && !recurMatch(next.getMinutes(), this.minute)) {\n      next.addMinute();\n      continue;\n    }\n    if (this.second != null && !recurMatch(next.getSeconds(), this.second)) {\n      next.addSecond();\n      continue;\n    }\n    break;\n  }\n  return next;\n};\nfunction recurMatch(val, matcher) {\n  if (matcher == null) {\n    return true;\n  }\n  if (typeof matcher === 'number') {\n    return val === matcher;\n  } else if (typeof matcher === 'string') {\n    return val === Number(matcher);\n  } else if (matcher instanceof Range) {\n    return matcher.contains(val);\n  } else if (Array.isArray(matcher) || matcher instanceof Array) {\n    for (let i = 0; i < matcher.length; i++) {\n      if (recurMatch(val, matcher[i])) {\n        return true;\n      }\n    }\n  }\n  return false;\n}\n\n/* Date-based scheduler */\nfunction runOnDate(date, job) {\n  const now = Date.now();\n  const then = date.getTime();\n  return lt.setTimeout(function () {\n    if (then > Date.now()) runOnDate(date, job);else job();\n  }, then < now ? 0 : then - now);\n}\nfunction scheduleInvocation(invocation) {\n  sorted.add(invocations, invocation, sorter);\n  prepareNextInvocation();\n  const date = invocation.fireDate instanceof CronDate ? invocation.fireDate.toDate() : invocation.fireDate;\n  invocation.job.emit('scheduled', date);\n}\nfunction prepareNextInvocation() {\n  if (invocations.length > 0 && currentInvocation !== invocations[0]) {\n    if (currentInvocation !== null) {\n      lt.clearTimeout(currentInvocation.timerID);\n      currentInvocation.timerID = null;\n      currentInvocation = null;\n    }\n    currentInvocation = invocations[0];\n    const job = currentInvocation.job;\n    const cinv = currentInvocation;\n    currentInvocation.timerID = runOnDate(currentInvocation.fireDate, function () {\n      currentInvocationFinished();\n      if (job.callback) {\n        job.callback();\n      }\n      if (cinv.recurrenceRule.recurs || cinv.recurrenceRule._endDate === null) {\n        const inv = scheduleNextRecurrence(cinv.recurrenceRule, cinv.job, cinv.fireDate, cinv.endDate);\n        if (inv !== null) {\n          inv.job.trackInvocation(inv);\n        }\n      }\n      job.stopTrackingInvocation(cinv);\n      try {\n        const result = job.invoke(cinv.fireDate instanceof CronDate ? cinv.fireDate.toDate() : cinv.fireDate);\n        job.emit('run');\n        job.running += 1;\n        if (result instanceof Promise) {\n          result.then(function (value) {\n            job.emit('success', value);\n            job.running -= 1;\n          }).catch(function (err) {\n            job.emit('error', err);\n            job.running -= 1;\n          });\n        } else {\n          job.emit('success', result);\n          job.running -= 1;\n        }\n      } catch (err) {\n        job.emit('error', err);\n        job.running -= 1;\n      }\n      if (job.isOneTimeJob) {\n        job.deleteFromSchedule();\n      }\n    });\n  }\n}\nfunction currentInvocationFinished() {\n  invocations.shift();\n  currentInvocation = null;\n  prepareNextInvocation();\n}\nfunction cancelInvocation(invocation) {\n  const idx = invocations.indexOf(invocation);\n  if (idx > -1) {\n    invocations.splice(idx, 1);\n    if (invocation.timerID !== null) {\n      lt.clearTimeout(invocation.timerID);\n    }\n    if (currentInvocation === invocation) {\n      currentInvocation = null;\n    }\n    invocation.job.emit('canceled', invocation.fireDate);\n    prepareNextInvocation();\n  }\n}\n\n/* Recurrence scheduler */\nfunction scheduleNextRecurrence(rule, job, prevDate, endDate) {\n  prevDate = prevDate instanceof CronDate ? prevDate : new CronDate();\n  const date = rule instanceof RecurrenceRule ? rule._nextInvocationDate(prevDate) : rule.next();\n  if (date === null) {\n    return null;\n  }\n  if (endDate instanceof CronDate && date.getTime() > endDate.getTime()) {\n    return null;\n  }\n  const inv = new Invocation(job, date, rule, endDate);\n  scheduleInvocation(inv);\n  return inv;\n}\nmodule.exports = {\n  Range,\n  RecurrenceRule,\n  Invocation,\n  cancelInvocation,\n  scheduleInvocation,\n  scheduleNextRecurrence,\n  sorter,\n  _invocations: invocations\n};", "map": {"version": 3, "names": ["lt", "require", "CronDate", "sorted", "invocations", "currentInvocation", "DoesntRecur", "RecurrenceRule", "recurs", "Invocation", "job", "fireDate", "recurrenceRule", "endDate", "timerID", "sorter", "a", "b", "getTime", "Range", "start", "end", "step", "prototype", "contains", "val", "i", "year", "month", "date", "dayOfWeek", "hour", "minute", "second", "<PERSON><PERSON><PERSON><PERSON>", "isValidType", "num", "Array", "isArray", "every", "e", "Number", "isNaN", "nextInvocationDate", "base", "next", "_nextInvocationDate", "toDate", "Date", "now", "tz", "fullYear", "getFullYear", "addSecond", "recurMatch", "addYear", "setMonth", "setDate", "setHours", "setMinutes", "setSeconds", "getMonth", "addMonth", "getDate", "addDay", "getDay", "getHours", "addHour", "getMinutes", "addMinute", "getSeconds", "matcher", "length", "runOnDate", "then", "setTimeout", "scheduleInvocation", "invocation", "add", "prepareNextInvocation", "emit", "clearTimeout", "cinv", "currentInvocationFinished", "callback", "_endDate", "inv", "scheduleNextRecurrence", "trackInvocation", "stopTrackingInvocation", "result", "invoke", "running", "Promise", "value", "catch", "err", "isOneTimeJob", "deleteFromSchedule", "shift", "cancelInvocation", "idx", "indexOf", "splice", "rule", "prevDate", "module", "exports", "_invocations"], "sources": ["C:/Users/<USER>/Documents/ekrani/node_modules/node-schedule/lib/Invocation.js"], "sourcesContent": ["'use strict';\n\nconst lt = require('long-timeout')\nconst CronDate = require('cron-parser/lib/date')\nconst sorted = require('sorted-array-functions')\n\nconst invocations = [];\nlet currentInvocation = null;\n\n/* DoesntRecur rule */\nconst DoesntRecur = new RecurrenceRule();\nDoesntRecur.recurs = false;\n\n/* Invocation object */\nfunction Invocation(job, fireDate, recurrenceRule, endDate) {\n  this.job = job;\n  this.fireDate = fireDate;\n  this.endDate = endDate;\n  this.recurrenceRule = recurrenceRule || DoesntRecur;\n\n  this.timerID = null;\n}\n\nfunction sorter(a, b) {\n  return (a.fireDate.getTime() - b.fireDate.getTime());\n}\n\n/* Range object */\nfunction Range(start, end, step) {\n  this.start = start || 0;\n  this.end = end || 60;\n  this.step = step || 1;\n}\n\nRange.prototype.contains = function(val) {\n  if (this.step === null || this.step === 1) {\n    return (val >= this.start && val <= this.end);\n  } else {\n    for (let i = this.start; i < this.end; i += this.step) {\n      if (i === val) {\n        return true;\n      }\n    }\n\n    return false;\n  }\n};\n\n/* RecurrenceRule object */\n/*\n  Interpreting each property:\n  null - any value is valid\n  number - fixed value\n  Range - value must fall in range\n  array - value must validate against any item in list\n\n  NOTE: Cron months are 1-based, but RecurrenceRule months are 0-based.\n*/\nfunction RecurrenceRule(year, month, date, dayOfWeek, hour, minute, second) {\n  this.recurs = true;\n\n  this.year = (year == null) ? null : year;\n  this.month = (month == null) ? null : month;\n  this.date = (date == null) ? null : date;\n  this.dayOfWeek = (dayOfWeek == null) ? null : dayOfWeek;\n  this.hour = (hour == null) ? null : hour;\n  this.minute = (minute == null) ? null : minute;\n  this.second = (second == null) ? 0 : second;\n}\n\nRecurrenceRule.prototype.isValid = function() {\n  function isValidType(num) {\n    if (Array.isArray(num) || (num instanceof Array)) {\n      return num.every(function(e) {\n        return isValidType(e);\n      });\n    }\n    return !(Number.isNaN(Number(num)) && !(num instanceof Range));\n  }\n  if (this.month !== null && (this.month < 0 || this.month > 11 || !isValidType(this.month))) {\n    return false;\n  }\n  if (this.dayOfWeek !== null && (this.dayOfWeek < 0 || this.dayOfWeek > 6 || !isValidType(this.dayOfWeek))) {\n    return false;\n  }\n  if (this.hour !== null && (this.hour < 0 || this.hour > 23 || !isValidType(this.hour))) {\n    return false;\n  }\n  if (this.minute !== null && (this.minute < 0 || this.minute > 59 || !isValidType(this.minute))) {\n    return false;\n  }\n  if (this.second !== null && (this.second < 0 || this.second > 59 || !isValidType(this.second))) {\n    return false;\n  }\n  if (this.date !== null) {\n    if(!isValidType(this.date)) {\n      return false;\n    }\n    switch (this.month) {\n      case 3:\n      case 5:\n      case 8:\n      case 10:\n        if (this.date < 1 || this. date > 30) {\n          return false;\n        }\n        break;\n      case 1:\n        if (this.date < 1 || this. date > 29) {\n          return false;\n        }\n        break;\n      default:\n        if (this.date < 1 || this. date > 31) {\n          return false;\n        }\n    }\n  }\n  return true;\n};\n\nRecurrenceRule.prototype.nextInvocationDate = function(base) {\n  const next = this._nextInvocationDate(base);\n  return next ? next.toDate() : null;\n};\n\nRecurrenceRule.prototype._nextInvocationDate = function(base) {\n  base = ((base instanceof CronDate) || (base instanceof Date)) ? base : (new Date());\n  if (!this.recurs) {\n    return null;\n  }\n\n  if(!this.isValid()) {\n    return null;\n  }\n\n  const now = new CronDate(Date.now(), this.tz);\n  let fullYear = now.getFullYear();\n  if ((this.year !== null) &&\n    (typeof this.year == 'number') &&\n    (this.year < fullYear)) {\n    return null;\n  }\n\n  let next = new CronDate(base.getTime(), this.tz);\n  next.addSecond();\n\n  while (true) {\n    if (this.year !== null) {\n      fullYear = next.getFullYear();\n      if ((typeof this.year == 'number') && (this.year < fullYear)) {\n        next = null;\n        break;\n      }\n\n      if (!recurMatch(fullYear, this.year)) {\n        next.addYear();\n        next.setMonth(0);\n        next.setDate(1);\n        next.setHours(0);\n        next.setMinutes(0);\n        next.setSeconds(0);\n        continue;\n      }\n    }\n    if (this.month != null && !recurMatch(next.getMonth(), this.month)) {\n      next.addMonth();\n      continue;\n    }\n    if (this.date != null && !recurMatch(next.getDate(), this.date)) {\n      next.addDay();\n      continue;\n    }\n    if (this.dayOfWeek != null && !recurMatch(next.getDay(), this.dayOfWeek)) {\n      next.addDay();\n      continue;\n    }\n    if (this.hour != null && !recurMatch(next.getHours(), this.hour)) {\n      next.addHour();\n      continue;\n    }\n    if (this.minute != null && !recurMatch(next.getMinutes(), this.minute)) {\n      next.addMinute();\n      continue;\n    }\n    if (this.second != null && !recurMatch(next.getSeconds(), this.second)) {\n      next.addSecond();\n      continue;\n    }\n\n    break;\n  }\n\n  return next;\n};\n\nfunction recurMatch(val, matcher) {\n  if (matcher == null) {\n    return true;\n  }\n\n  if (typeof matcher === 'number') {\n    return (val === matcher);\n  } else if(typeof matcher === 'string') {\n    return (val === Number(matcher));\n  } else if (matcher instanceof Range) {\n    return matcher.contains(val);\n  } else if (Array.isArray(matcher) || (matcher instanceof Array)) {\n    for (let i = 0; i < matcher.length; i++) {\n      if (recurMatch(val, matcher[i])) {\n        return true;\n      }\n    }\n  }\n\n  return false;\n}\n\n/* Date-based scheduler */\nfunction runOnDate(date, job) {\n  const now = Date.now();\n  const then = date.getTime();\n\n  return lt.setTimeout(function() {\n    if (then > Date.now())\n      runOnDate(date, job);\n    else\n      job();\n  }, (then < now ? 0 : then - now));\n}\n\nfunction scheduleInvocation(invocation) {\n  sorted.add(invocations, invocation, sorter);\n  prepareNextInvocation();\n  const date = invocation.fireDate instanceof CronDate ? invocation.fireDate.toDate() : invocation.fireDate;\n  invocation.job.emit('scheduled', date);\n}\n\nfunction prepareNextInvocation() {\n  if (invocations.length > 0 && currentInvocation !== invocations[0]) {\n    if (currentInvocation !== null) {\n      lt.clearTimeout(currentInvocation.timerID);\n      currentInvocation.timerID = null;\n      currentInvocation = null;\n    }\n\n    currentInvocation = invocations[0];\n\n    const job = currentInvocation.job;\n    const cinv = currentInvocation;\n    currentInvocation.timerID = runOnDate(currentInvocation.fireDate, function() {\n      currentInvocationFinished();\n\n      if (job.callback) {\n        job.callback();\n      }\n\n      if (cinv.recurrenceRule.recurs || cinv.recurrenceRule._endDate === null) {\n        const inv = scheduleNextRecurrence(cinv.recurrenceRule, cinv.job, cinv.fireDate, cinv.endDate);\n        if (inv !== null) {\n          inv.job.trackInvocation(inv);\n        }\n      }\n\n      job.stopTrackingInvocation(cinv);\n\n      try {\n        const result = job.invoke(cinv.fireDate instanceof CronDate ? cinv.fireDate.toDate() : cinv.fireDate);\n        job.emit('run');\n        job.running += 1;\n\n        if (result instanceof Promise) {\n          result.then(function (value) {\n            job.emit('success', value);\n            job.running -= 1;\n          }).catch(function (err) {\n            job.emit('error', err);\n            job.running -= 1;\n          });\n        } else {\n          job.emit('success', result);\n          job.running -= 1;\n        }\n      } catch (err) {\n        job.emit('error', err);\n        job.running -= 1;\n      }\n\n      if (job.isOneTimeJob) {\n        job.deleteFromSchedule();\n      }\n    });\n  }\n}\n\nfunction currentInvocationFinished() {\n  invocations.shift();\n  currentInvocation = null;\n  prepareNextInvocation();\n}\n\nfunction cancelInvocation(invocation) {\n  const idx = invocations.indexOf(invocation);\n  if (idx > -1) {\n    invocations.splice(idx, 1);\n    if (invocation.timerID !== null) {\n      lt.clearTimeout(invocation.timerID);\n    }\n\n    if (currentInvocation === invocation) {\n      currentInvocation = null;\n    }\n\n    invocation.job.emit('canceled', invocation.fireDate);\n    prepareNextInvocation();\n  }\n}\n\n/* Recurrence scheduler */\nfunction scheduleNextRecurrence(rule, job, prevDate, endDate) {\n\n  prevDate = (prevDate instanceof CronDate) ? prevDate : new CronDate();\n\n  const date = (rule instanceof RecurrenceRule) ? rule._nextInvocationDate(prevDate) : rule.next();\n  if (date === null) {\n    return null;\n  }\n\n  if ((endDate instanceof CronDate) && date.getTime() > endDate.getTime()) {\n    return null;\n  }\n\n  const inv = new Invocation(job, date, rule, endDate);\n  scheduleInvocation(inv);\n\n  return inv;\n}\n\nmodule.exports = {\n  Range,\n  RecurrenceRule,\n  Invocation,\n  cancelInvocation,\n  scheduleInvocation,\n  scheduleNextRecurrence,\n  sorter,\n  _invocations: invocations\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,EAAE,GAAGC,OAAO,CAAC,cAAc,CAAC;AAClC,MAAMC,QAAQ,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAChD,MAAME,MAAM,GAAGF,OAAO,CAAC,wBAAwB,CAAC;AAEhD,MAAMG,WAAW,GAAG,EAAE;AACtB,IAAIC,iBAAiB,GAAG,IAAI;;AAE5B;AACA,MAAMC,WAAW,GAAG,IAAIC,cAAc,CAAC,CAAC;AACxCD,WAAW,CAACE,MAAM,GAAG,KAAK;;AAE1B;AACA,SAASC,UAAUA,CAACC,GAAG,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,OAAO,EAAE;EAC1D,IAAI,CAACH,GAAG,GAAGA,GAAG;EACd,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EACxB,IAAI,CAACE,OAAO,GAAGA,OAAO;EACtB,IAAI,CAACD,cAAc,GAAGA,cAAc,IAAIN,WAAW;EAEnD,IAAI,CAACQ,OAAO,GAAG,IAAI;AACrB;AAEA,SAASC,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACpB,OAAQD,CAAC,CAACL,QAAQ,CAACO,OAAO,CAAC,CAAC,GAAGD,CAAC,CAACN,QAAQ,CAACO,OAAO,CAAC,CAAC;AACrD;;AAEA;AACA,SAASC,KAAKA,CAACC,KAAK,EAAEC,GAAG,EAAEC,IAAI,EAAE;EAC/B,IAAI,CAACF,KAAK,GAAGA,KAAK,IAAI,CAAC;EACvB,IAAI,CAACC,GAAG,GAAGA,GAAG,IAAI,EAAE;EACpB,IAAI,CAACC,IAAI,GAAGA,IAAI,IAAI,CAAC;AACvB;AAEAH,KAAK,CAACI,SAAS,CAACC,QAAQ,GAAG,UAASC,GAAG,EAAE;EACvC,IAAI,IAAI,CAACH,IAAI,KAAK,IAAI,IAAI,IAAI,CAACA,IAAI,KAAK,CAAC,EAAE;IACzC,OAAQG,GAAG,IAAI,IAAI,CAACL,KAAK,IAAIK,GAAG,IAAI,IAAI,CAACJ,GAAG;EAC9C,CAAC,MAAM;IACL,KAAK,IAAIK,CAAC,GAAG,IAAI,CAACN,KAAK,EAAEM,CAAC,GAAG,IAAI,CAACL,GAAG,EAAEK,CAAC,IAAI,IAAI,CAACJ,IAAI,EAAE;MACrD,IAAII,CAAC,KAAKD,GAAG,EAAE;QACb,OAAO,IAAI;MACb;IACF;IAEA,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASlB,cAAcA,CAACoB,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAE;EAC1E,IAAI,CAACzB,MAAM,GAAG,IAAI;EAElB,IAAI,CAACmB,IAAI,GAAIA,IAAI,IAAI,IAAI,GAAI,IAAI,GAAGA,IAAI;EACxC,IAAI,CAACC,KAAK,GAAIA,KAAK,IAAI,IAAI,GAAI,IAAI,GAAGA,KAAK;EAC3C,IAAI,CAACC,IAAI,GAAIA,IAAI,IAAI,IAAI,GAAI,IAAI,GAAGA,IAAI;EACxC,IAAI,CAACC,SAAS,GAAIA,SAAS,IAAI,IAAI,GAAI,IAAI,GAAGA,SAAS;EACvD,IAAI,CAACC,IAAI,GAAIA,IAAI,IAAI,IAAI,GAAI,IAAI,GAAGA,IAAI;EACxC,IAAI,CAACC,MAAM,GAAIA,MAAM,IAAI,IAAI,GAAI,IAAI,GAAGA,MAAM;EAC9C,IAAI,CAACC,MAAM,GAAIA,MAAM,IAAI,IAAI,GAAI,CAAC,GAAGA,MAAM;AAC7C;AAEA1B,cAAc,CAACgB,SAAS,CAACW,OAAO,GAAG,YAAW;EAC5C,SAASC,WAAWA,CAACC,GAAG,EAAE;IACxB,IAAIC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,IAAKA,GAAG,YAAYC,KAAM,EAAE;MAChD,OAAOD,GAAG,CAACG,KAAK,CAAC,UAASC,CAAC,EAAE;QAC3B,OAAOL,WAAW,CAACK,CAAC,CAAC;MACvB,CAAC,CAAC;IACJ;IACA,OAAO,EAAEC,MAAM,CAACC,KAAK,CAACD,MAAM,CAACL,GAAG,CAAC,CAAC,IAAI,EAAEA,GAAG,YAAYjB,KAAK,CAAC,CAAC;EAChE;EACA,IAAI,IAAI,CAACS,KAAK,KAAK,IAAI,KAAK,IAAI,CAACA,KAAK,GAAG,CAAC,IAAI,IAAI,CAACA,KAAK,GAAG,EAAE,IAAI,CAACO,WAAW,CAAC,IAAI,CAACP,KAAK,CAAC,CAAC,EAAE;IAC1F,OAAO,KAAK;EACd;EACA,IAAI,IAAI,CAACE,SAAS,KAAK,IAAI,KAAK,IAAI,CAACA,SAAS,GAAG,CAAC,IAAI,IAAI,CAACA,SAAS,GAAG,CAAC,IAAI,CAACK,WAAW,CAAC,IAAI,CAACL,SAAS,CAAC,CAAC,EAAE;IACzG,OAAO,KAAK;EACd;EACA,IAAI,IAAI,CAACC,IAAI,KAAK,IAAI,KAAK,IAAI,CAACA,IAAI,GAAG,CAAC,IAAI,IAAI,CAACA,IAAI,GAAG,EAAE,IAAI,CAACI,WAAW,CAAC,IAAI,CAACJ,IAAI,CAAC,CAAC,EAAE;IACtF,OAAO,KAAK;EACd;EACA,IAAI,IAAI,CAACC,MAAM,KAAK,IAAI,KAAK,IAAI,CAACA,MAAM,GAAG,CAAC,IAAI,IAAI,CAACA,MAAM,GAAG,EAAE,IAAI,CAACG,WAAW,CAAC,IAAI,CAACH,MAAM,CAAC,CAAC,EAAE;IAC9F,OAAO,KAAK;EACd;EACA,IAAI,IAAI,CAACC,MAAM,KAAK,IAAI,KAAK,IAAI,CAACA,MAAM,GAAG,CAAC,IAAI,IAAI,CAACA,MAAM,GAAG,EAAE,IAAI,CAACE,WAAW,CAAC,IAAI,CAACF,MAAM,CAAC,CAAC,EAAE;IAC9F,OAAO,KAAK;EACd;EACA,IAAI,IAAI,CAACJ,IAAI,KAAK,IAAI,EAAE;IACtB,IAAG,CAACM,WAAW,CAAC,IAAI,CAACN,IAAI,CAAC,EAAE;MAC1B,OAAO,KAAK;IACd;IACA,QAAQ,IAAI,CAACD,KAAK;MAChB,KAAK,CAAC;MACN,KAAK,CAAC;MACN,KAAK,CAAC;MACN,KAAK,EAAE;QACL,IAAI,IAAI,CAACC,IAAI,GAAG,CAAC,IAAI,IAAI,CAAEA,IAAI,GAAG,EAAE,EAAE;UACpC,OAAO,KAAK;QACd;QACA;MACF,KAAK,CAAC;QACJ,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,IAAI,IAAI,CAAEA,IAAI,GAAG,EAAE,EAAE;UACpC,OAAO,KAAK;QACd;QACA;MACF;QACE,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,IAAI,IAAI,CAAEA,IAAI,GAAG,EAAE,EAAE;UACpC,OAAO,KAAK;QACd;IACJ;EACF;EACA,OAAO,IAAI;AACb,CAAC;AAEDtB,cAAc,CAACgB,SAAS,CAACoB,kBAAkB,GAAG,UAASC,IAAI,EAAE;EAC3D,MAAMC,IAAI,GAAG,IAAI,CAACC,mBAAmB,CAACF,IAAI,CAAC;EAC3C,OAAOC,IAAI,GAAGA,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,IAAI;AACpC,CAAC;AAEDxC,cAAc,CAACgB,SAAS,CAACuB,mBAAmB,GAAG,UAASF,IAAI,EAAE;EAC5DA,IAAI,GAAKA,IAAI,YAAY1C,QAAQ,IAAM0C,IAAI,YAAYI,IAAK,GAAIJ,IAAI,GAAI,IAAII,IAAI,CAAC,CAAE;EACnF,IAAI,CAAC,IAAI,CAACxC,MAAM,EAAE;IAChB,OAAO,IAAI;EACb;EAEA,IAAG,CAAC,IAAI,CAAC0B,OAAO,CAAC,CAAC,EAAE;IAClB,OAAO,IAAI;EACb;EAEA,MAAMe,GAAG,GAAG,IAAI/C,QAAQ,CAAC8C,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACC,EAAE,CAAC;EAC7C,IAAIC,QAAQ,GAAGF,GAAG,CAACG,WAAW,CAAC,CAAC;EAChC,IAAK,IAAI,CAACzB,IAAI,KAAK,IAAI,IACpB,OAAO,IAAI,CAACA,IAAI,IAAI,QAAS,IAC7B,IAAI,CAACA,IAAI,GAAGwB,QAAS,EAAE;IACxB,OAAO,IAAI;EACb;EAEA,IAAIN,IAAI,GAAG,IAAI3C,QAAQ,CAAC0C,IAAI,CAAC1B,OAAO,CAAC,CAAC,EAAE,IAAI,CAACgC,EAAE,CAAC;EAChDL,IAAI,CAACQ,SAAS,CAAC,CAAC;EAEhB,OAAO,IAAI,EAAE;IACX,IAAI,IAAI,CAAC1B,IAAI,KAAK,IAAI,EAAE;MACtBwB,QAAQ,GAAGN,IAAI,CAACO,WAAW,CAAC,CAAC;MAC7B,IAAK,OAAO,IAAI,CAACzB,IAAI,IAAI,QAAQ,IAAM,IAAI,CAACA,IAAI,GAAGwB,QAAS,EAAE;QAC5DN,IAAI,GAAG,IAAI;QACX;MACF;MAEA,IAAI,CAACS,UAAU,CAACH,QAAQ,EAAE,IAAI,CAACxB,IAAI,CAAC,EAAE;QACpCkB,IAAI,CAACU,OAAO,CAAC,CAAC;QACdV,IAAI,CAACW,QAAQ,CAAC,CAAC,CAAC;QAChBX,IAAI,CAACY,OAAO,CAAC,CAAC,CAAC;QACfZ,IAAI,CAACa,QAAQ,CAAC,CAAC,CAAC;QAChBb,IAAI,CAACc,UAAU,CAAC,CAAC,CAAC;QAClBd,IAAI,CAACe,UAAU,CAAC,CAAC,CAAC;QAClB;MACF;IACF;IACA,IAAI,IAAI,CAAChC,KAAK,IAAI,IAAI,IAAI,CAAC0B,UAAU,CAACT,IAAI,CAACgB,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACjC,KAAK,CAAC,EAAE;MAClEiB,IAAI,CAACiB,QAAQ,CAAC,CAAC;MACf;IACF;IACA,IAAI,IAAI,CAACjC,IAAI,IAAI,IAAI,IAAI,CAACyB,UAAU,CAACT,IAAI,CAACkB,OAAO,CAAC,CAAC,EAAE,IAAI,CAAClC,IAAI,CAAC,EAAE;MAC/DgB,IAAI,CAACmB,MAAM,CAAC,CAAC;MACb;IACF;IACA,IAAI,IAAI,CAAClC,SAAS,IAAI,IAAI,IAAI,CAACwB,UAAU,CAACT,IAAI,CAACoB,MAAM,CAAC,CAAC,EAAE,IAAI,CAACnC,SAAS,CAAC,EAAE;MACxEe,IAAI,CAACmB,MAAM,CAAC,CAAC;MACb;IACF;IACA,IAAI,IAAI,CAACjC,IAAI,IAAI,IAAI,IAAI,CAACuB,UAAU,CAACT,IAAI,CAACqB,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACnC,IAAI,CAAC,EAAE;MAChEc,IAAI,CAACsB,OAAO,CAAC,CAAC;MACd;IACF;IACA,IAAI,IAAI,CAACnC,MAAM,IAAI,IAAI,IAAI,CAACsB,UAAU,CAACT,IAAI,CAACuB,UAAU,CAAC,CAAC,EAAE,IAAI,CAACpC,MAAM,CAAC,EAAE;MACtEa,IAAI,CAACwB,SAAS,CAAC,CAAC;MAChB;IACF;IACA,IAAI,IAAI,CAACpC,MAAM,IAAI,IAAI,IAAI,CAACqB,UAAU,CAACT,IAAI,CAACyB,UAAU,CAAC,CAAC,EAAE,IAAI,CAACrC,MAAM,CAAC,EAAE;MACtEY,IAAI,CAACQ,SAAS,CAAC,CAAC;MAChB;IACF;IAEA;EACF;EAEA,OAAOR,IAAI;AACb,CAAC;AAED,SAASS,UAAUA,CAAC7B,GAAG,EAAE8C,OAAO,EAAE;EAChC,IAAIA,OAAO,IAAI,IAAI,EAAE;IACnB,OAAO,IAAI;EACb;EAEA,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC/B,OAAQ9C,GAAG,KAAK8C,OAAO;EACzB,CAAC,MAAM,IAAG,OAAOA,OAAO,KAAK,QAAQ,EAAE;IACrC,OAAQ9C,GAAG,KAAKgB,MAAM,CAAC8B,OAAO,CAAC;EACjC,CAAC,MAAM,IAAIA,OAAO,YAAYpD,KAAK,EAAE;IACnC,OAAOoD,OAAO,CAAC/C,QAAQ,CAACC,GAAG,CAAC;EAC9B,CAAC,MAAM,IAAIY,KAAK,CAACC,OAAO,CAACiC,OAAO,CAAC,IAAKA,OAAO,YAAYlC,KAAM,EAAE;IAC/D,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6C,OAAO,CAACC,MAAM,EAAE9C,CAAC,EAAE,EAAE;MACvC,IAAI4B,UAAU,CAAC7B,GAAG,EAAE8C,OAAO,CAAC7C,CAAC,CAAC,CAAC,EAAE;QAC/B,OAAO,IAAI;MACb;IACF;EACF;EAEA,OAAO,KAAK;AACd;;AAEA;AACA,SAAS+C,SAASA,CAAC5C,IAAI,EAAEnB,GAAG,EAAE;EAC5B,MAAMuC,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;EACtB,MAAMyB,IAAI,GAAG7C,IAAI,CAACX,OAAO,CAAC,CAAC;EAE3B,OAAOlB,EAAE,CAAC2E,UAAU,CAAC,YAAW;IAC9B,IAAID,IAAI,GAAG1B,IAAI,CAACC,GAAG,CAAC,CAAC,EACnBwB,SAAS,CAAC5C,IAAI,EAAEnB,GAAG,CAAC,CAAC,KAErBA,GAAG,CAAC,CAAC;EACT,CAAC,EAAGgE,IAAI,GAAGzB,GAAG,GAAG,CAAC,GAAGyB,IAAI,GAAGzB,GAAI,CAAC;AACnC;AAEA,SAAS2B,kBAAkBA,CAACC,UAAU,EAAE;EACtC1E,MAAM,CAAC2E,GAAG,CAAC1E,WAAW,EAAEyE,UAAU,EAAE9D,MAAM,CAAC;EAC3CgE,qBAAqB,CAAC,CAAC;EACvB,MAAMlD,IAAI,GAAGgD,UAAU,CAAClE,QAAQ,YAAYT,QAAQ,GAAG2E,UAAU,CAAClE,QAAQ,CAACoC,MAAM,CAAC,CAAC,GAAG8B,UAAU,CAAClE,QAAQ;EACzGkE,UAAU,CAACnE,GAAG,CAACsE,IAAI,CAAC,WAAW,EAAEnD,IAAI,CAAC;AACxC;AAEA,SAASkD,qBAAqBA,CAAA,EAAG;EAC/B,IAAI3E,WAAW,CAACoE,MAAM,GAAG,CAAC,IAAInE,iBAAiB,KAAKD,WAAW,CAAC,CAAC,CAAC,EAAE;IAClE,IAAIC,iBAAiB,KAAK,IAAI,EAAE;MAC9BL,EAAE,CAACiF,YAAY,CAAC5E,iBAAiB,CAACS,OAAO,CAAC;MAC1CT,iBAAiB,CAACS,OAAO,GAAG,IAAI;MAChCT,iBAAiB,GAAG,IAAI;IAC1B;IAEAA,iBAAiB,GAAGD,WAAW,CAAC,CAAC,CAAC;IAElC,MAAMM,GAAG,GAAGL,iBAAiB,CAACK,GAAG;IACjC,MAAMwE,IAAI,GAAG7E,iBAAiB;IAC9BA,iBAAiB,CAACS,OAAO,GAAG2D,SAAS,CAACpE,iBAAiB,CAACM,QAAQ,EAAE,YAAW;MAC3EwE,yBAAyB,CAAC,CAAC;MAE3B,IAAIzE,GAAG,CAAC0E,QAAQ,EAAE;QAChB1E,GAAG,CAAC0E,QAAQ,CAAC,CAAC;MAChB;MAEA,IAAIF,IAAI,CAACtE,cAAc,CAACJ,MAAM,IAAI0E,IAAI,CAACtE,cAAc,CAACyE,QAAQ,KAAK,IAAI,EAAE;QACvE,MAAMC,GAAG,GAAGC,sBAAsB,CAACL,IAAI,CAACtE,cAAc,EAAEsE,IAAI,CAACxE,GAAG,EAAEwE,IAAI,CAACvE,QAAQ,EAAEuE,IAAI,CAACrE,OAAO,CAAC;QAC9F,IAAIyE,GAAG,KAAK,IAAI,EAAE;UAChBA,GAAG,CAAC5E,GAAG,CAAC8E,eAAe,CAACF,GAAG,CAAC;QAC9B;MACF;MAEA5E,GAAG,CAAC+E,sBAAsB,CAACP,IAAI,CAAC;MAEhC,IAAI;QACF,MAAMQ,MAAM,GAAGhF,GAAG,CAACiF,MAAM,CAACT,IAAI,CAACvE,QAAQ,YAAYT,QAAQ,GAAGgF,IAAI,CAACvE,QAAQ,CAACoC,MAAM,CAAC,CAAC,GAAGmC,IAAI,CAACvE,QAAQ,CAAC;QACrGD,GAAG,CAACsE,IAAI,CAAC,KAAK,CAAC;QACftE,GAAG,CAACkF,OAAO,IAAI,CAAC;QAEhB,IAAIF,MAAM,YAAYG,OAAO,EAAE;UAC7BH,MAAM,CAAChB,IAAI,CAAC,UAAUoB,KAAK,EAAE;YAC3BpF,GAAG,CAACsE,IAAI,CAAC,SAAS,EAAEc,KAAK,CAAC;YAC1BpF,GAAG,CAACkF,OAAO,IAAI,CAAC;UAClB,CAAC,CAAC,CAACG,KAAK,CAAC,UAAUC,GAAG,EAAE;YACtBtF,GAAG,CAACsE,IAAI,CAAC,OAAO,EAAEgB,GAAG,CAAC;YACtBtF,GAAG,CAACkF,OAAO,IAAI,CAAC;UAClB,CAAC,CAAC;QACJ,CAAC,MAAM;UACLlF,GAAG,CAACsE,IAAI,CAAC,SAAS,EAAEU,MAAM,CAAC;UAC3BhF,GAAG,CAACkF,OAAO,IAAI,CAAC;QAClB;MACF,CAAC,CAAC,OAAOI,GAAG,EAAE;QACZtF,GAAG,CAACsE,IAAI,CAAC,OAAO,EAAEgB,GAAG,CAAC;QACtBtF,GAAG,CAACkF,OAAO,IAAI,CAAC;MAClB;MAEA,IAAIlF,GAAG,CAACuF,YAAY,EAAE;QACpBvF,GAAG,CAACwF,kBAAkB,CAAC,CAAC;MAC1B;IACF,CAAC,CAAC;EACJ;AACF;AAEA,SAASf,yBAAyBA,CAAA,EAAG;EACnC/E,WAAW,CAAC+F,KAAK,CAAC,CAAC;EACnB9F,iBAAiB,GAAG,IAAI;EACxB0E,qBAAqB,CAAC,CAAC;AACzB;AAEA,SAASqB,gBAAgBA,CAACvB,UAAU,EAAE;EACpC,MAAMwB,GAAG,GAAGjG,WAAW,CAACkG,OAAO,CAACzB,UAAU,CAAC;EAC3C,IAAIwB,GAAG,GAAG,CAAC,CAAC,EAAE;IACZjG,WAAW,CAACmG,MAAM,CAACF,GAAG,EAAE,CAAC,CAAC;IAC1B,IAAIxB,UAAU,CAAC/D,OAAO,KAAK,IAAI,EAAE;MAC/Bd,EAAE,CAACiF,YAAY,CAACJ,UAAU,CAAC/D,OAAO,CAAC;IACrC;IAEA,IAAIT,iBAAiB,KAAKwE,UAAU,EAAE;MACpCxE,iBAAiB,GAAG,IAAI;IAC1B;IAEAwE,UAAU,CAACnE,GAAG,CAACsE,IAAI,CAAC,UAAU,EAAEH,UAAU,CAAClE,QAAQ,CAAC;IACpDoE,qBAAqB,CAAC,CAAC;EACzB;AACF;;AAEA;AACA,SAASQ,sBAAsBA,CAACiB,IAAI,EAAE9F,GAAG,EAAE+F,QAAQ,EAAE5F,OAAO,EAAE;EAE5D4F,QAAQ,GAAIA,QAAQ,YAAYvG,QAAQ,GAAIuG,QAAQ,GAAG,IAAIvG,QAAQ,CAAC,CAAC;EAErE,MAAM2B,IAAI,GAAI2E,IAAI,YAAYjG,cAAc,GAAIiG,IAAI,CAAC1D,mBAAmB,CAAC2D,QAAQ,CAAC,GAAGD,IAAI,CAAC3D,IAAI,CAAC,CAAC;EAChG,IAAIhB,IAAI,KAAK,IAAI,EAAE;IACjB,OAAO,IAAI;EACb;EAEA,IAAKhB,OAAO,YAAYX,QAAQ,IAAK2B,IAAI,CAACX,OAAO,CAAC,CAAC,GAAGL,OAAO,CAACK,OAAO,CAAC,CAAC,EAAE;IACvE,OAAO,IAAI;EACb;EAEA,MAAMoE,GAAG,GAAG,IAAI7E,UAAU,CAACC,GAAG,EAAEmB,IAAI,EAAE2E,IAAI,EAAE3F,OAAO,CAAC;EACpD+D,kBAAkB,CAACU,GAAG,CAAC;EAEvB,OAAOA,GAAG;AACZ;AAEAoB,MAAM,CAACC,OAAO,GAAG;EACfxF,KAAK;EACLZ,cAAc;EACdE,UAAU;EACV2F,gBAAgB;EAChBxB,kBAAkB;EAClBW,sBAAsB;EACtBxE,MAAM;EACN6F,YAAY,EAAExG;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}