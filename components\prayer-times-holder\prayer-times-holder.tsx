import { useEffect, useState } from "react";
import moment from "moment";
import "moment/min/locales";

import "./prayer-times-holder-style.css";
import {
  getActivePrayer,
  getTodayPrayerTimes,
} from "../../utils/prayer-times.service";
import { Prayer } from "../../utils/prayer.model";
import PrayerTime from "./prayer-time";
import { PrayerLable } from "../../utils/prayer-lable.enum";
import AkshamiIcon from "../../assets/icons/akshami.svg";
import SabahuIcon from "../../assets/icons/sabahu.svg";
import DrekaIcon from "../../assets/icons/dreka.svg";
import JaciaIcon from "../../assets/icons/jacia.svg";
import IkindiaIcon from "../../assets/icons/ikindia.svg";
import ImsakuIcon from "../../assets/icons/imsaku.svg";
import { formatIslamicDate } from "../../utils/format-hijri";
import { FormDataStorage } from "../../pages/register/FormPage";
import { setupMidnightInterval } from "../../utils/mid-night";

const PrayerTimesHolder = () => {
  const formData: FormDataStorage = JSON.parse(
    localStorage.getItem("formData") || "null"
  );

  const today = moment().locale("sq"); // Assuming today's date

  // Format date in Albanian
  const formattedDateSq = today.format("D, MMMM YYYY / dddd");

  const [prayerData, setPrayerData] = useState<Prayer>();
  // const [prayerDataLoading, setPrayerDataLoading] = useState(false);
  const [activePrayer, setActivePrayer] = useState<any>();

  const getPrayerTimes = async () => {
    const today = new Date(Date.now());
    const response = getTodayPrayerTimes() as Prayer;

    const getSecondInMilisecond = today.getSeconds() * 1000;
    const getMinuteInMilisecond = 60000;
    const waitTime = getMinuteInMilisecond - getSecondInMilisecond;

    const active = getActivePrayer();
    setActivePrayer(active);

    setTimeout(() => {
      // Initial execution of the function
      const timeoutActivePrayer = getActivePrayer();
      setActivePrayer(timeoutActivePrayer);

      // Execute the function every minute
      setInterval(() => {
        const intervalActivePrayer = getActivePrayer();
        setActivePrayer(intervalActivePrayer);
      }, getMinuteInMilisecond);
    }, waitTime);

    setPrayerData(response);
    // setPrayerDataLoading(false);
    // setPrayerDataLoading(false);
  };

  function intervalCallback() {
    setupMidnightInterval(getPrayerTimes, intervalCallback);
  }

  useEffect(() => {
    getPrayerTimes();
    // Initial setup
    const cleanup = setupMidnightInterval(getPrayerTimes, intervalCallback);

    // Cleanup the interval on component unmount
    return () => {
      cleanup();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <div className="prayer-times__container">
        <div className="date-time">
          <p>{formattedDateSq}</p>
          <p>{formatIslamicDate(new Date())}</p>
        </div>

        <div className="prayer-time-center">
          <PrayerTime
            title={PrayerLable[0]}
            time={prayerData?.todayPrayerTimes?.Imsaku as string}
            icon={ImsakuIcon}
            active={activePrayer === PrayerLable[0]}
          />
          <PrayerTime
            title={PrayerLable[2]}
            time={
              prayerData?.todayPrayerTimes
                ? (prayerData?.todayPrayerTimes["L. e Diellit"] as string)
                : ""
            }
            icon={SabahuIcon}
            active={activePrayer === PrayerLable[2]}
          />
          <PrayerTime
            title={PrayerLable[3]}
            time={prayerData?.todayPrayerTimes?.Dreka as string}
            icon={DrekaIcon}
            active={activePrayer === PrayerLable[3]}
            showIqamah={formData.showIqamah && formData.prayers?.dreka?.showOnTV}
            iqamahTime={formData.prayers?.dreka?.timeAfterAdhan}

          />
          <PrayerTime
            title={PrayerLable[4]}
            time={prayerData?.todayPrayerTimes?.Ikindia as string}
            icon={IkindiaIcon}
            active={activePrayer === PrayerLable[4]}
            showIqamah={formData.showIqamah && formData.prayers?.ikindia?.showOnTV}
            iqamahTime={formData.prayers?.ikindia?.timeAfterAdhan}
          />
          <PrayerTime
            title={PrayerLable[5]}
            time={prayerData?.todayPrayerTimes?.Akshami as string}
            icon={AkshamiIcon}
            showIqamah={formData.showIqamah && formData.prayers?.akshami?.showOnTV}
            iqamahTime={formData.prayers?.akshami?.timeAfterAdhan}
            active={activePrayer === PrayerLable[5]}
          />
          <PrayerTime
            title={PrayerLable[6]}
            time={prayerData?.todayPrayerTimes?.Jacia as string}
            icon={JaciaIcon}
            active={activePrayer === PrayerLable[6]}
            showIqamah={formData.showIqamah && formData.prayers?.jacia?.showOnTV}
            iqamahTime={formData.prayers?.jacia?.timeAfterAdhan}

          />
        </div>
        <div className="info-holder">
          <p>🕌 {`${formData?.mosqueName} - ${formData?.location}`}</p>
          <p>🎓 {formData?.name}</p>
        </div>
      </div>
    </>
  );
};

export default PrayerTimesHolder;
