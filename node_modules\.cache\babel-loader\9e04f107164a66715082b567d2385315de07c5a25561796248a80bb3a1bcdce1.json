{"ast": null, "code": "'use strict';\n\nfunction buildRange(item) {\n  return {\n    start: item,\n    count: 1\n  };\n}\nfunction completeRangeWithItem(range, item) {\n  range.end = item;\n  range.step = item - range.start;\n  range.count = 2;\n}\nfunction finalizeCurrentRange(results, currentRange, currentItemRange) {\n  if (currentRange) {\n    // Two elements do not form a range so split them into 2 single elements\n    if (currentRange.count === 2) {\n      results.push(buildRange(currentRange.start));\n      results.push(buildRange(currentRange.end));\n    } else {\n      results.push(currentRange);\n    }\n  }\n  if (currentItemRange) {\n    results.push(currentItemRange);\n  }\n}\nfunction compactField(arr) {\n  var results = [];\n  var currentRange = undefined;\n  for (var i = 0; i < arr.length; i++) {\n    var currentItem = arr[i];\n    if (typeof currentItem !== 'number') {\n      // String elements can't form a range\n      finalizeCurrentRange(results, currentRange, buildRange(currentItem));\n      currentRange = undefined;\n    } else if (!currentRange) {\n      // Start a new range\n      currentRange = buildRange(currentItem);\n    } else if (currentRange.count === 1) {\n      // Guess that the current item starts a range\n      completeRangeWithItem(currentRange, currentItem);\n    } else {\n      if (currentRange.step === currentItem - currentRange.end) {\n        // We found another item that matches the current range\n        currentRange.count++;\n        currentRange.end = currentItem;\n      } else if (currentRange.count === 2) {\n        // The current range can't be continued\n        // Break the first item of the current range into a single element, and try to start a new range with the second item\n        results.push(buildRange(currentRange.start));\n        currentRange = buildRange(currentRange.end);\n        completeRangeWithItem(currentRange, currentItem);\n      } else {\n        // Persist the current range and start a new one with current item\n        finalizeCurrentRange(results, currentRange);\n        currentRange = buildRange(currentItem);\n      }\n    }\n  }\n  finalizeCurrentRange(results, currentRange);\n  return results;\n}\nmodule.exports = compactField;", "map": {"version": 3, "names": ["buildRange", "item", "start", "count", "completeRangeWithItem", "range", "end", "step", "finalizeCurrentRange", "results", "currentRange", "currentItemRange", "push", "compactField", "arr", "undefined", "i", "length", "currentItem", "module", "exports"], "sources": ["C:/Users/<USER>/Documents/ekrani/node_modules/cron-parser/lib/field_compactor.js"], "sourcesContent": ["'use strict';\n\nfunction buildRange(item) {\n  return {\n    start: item,\n    count: 1\n  };\n}\n\nfunction completeRangeWithItem(range, item) {\n  range.end = item;\n  range.step = item - range.start;\n  range.count = 2;\n}\n\nfunction finalizeCurrentRange(results, currentRange, currentItemRange) {\n  if (currentRange) {\n    // Two elements do not form a range so split them into 2 single elements\n    if (currentRange.count === 2) {\n      results.push(buildRange(currentRange.start));\n      results.push(buildRange(currentRange.end));\n    } else {\n      results.push(currentRange);\n    }\n  }\n  if (currentItemRange) {\n    results.push(currentItemRange);\n  }\n}\n\nfunction compactField(arr) {\n  var results = [];\n  var currentRange = undefined;\n\n  for (var i = 0; i < arr.length; i++) {\n    var currentItem = arr[i];\n    if (typeof currentItem !== 'number') {\n      // String elements can't form a range\n      finalizeCurrentRange(results, currentRange, buildRange(currentItem));\n      currentRange = undefined;\n    } else if (!currentRange) {\n      // Start a new range\n      currentRange = buildRange(currentItem);\n    } else if (currentRange.count === 1) {\n      // Guess that the current item starts a range\n      completeRangeWithItem(currentRange, currentItem);\n    } else {\n      if (currentRange.step === currentItem - currentRange.end) {\n        // We found another item that matches the current range\n        currentRange.count++;\n        currentRange.end = currentItem;\n      } else if (currentRange.count === 2) { // The current range can't be continued\n        // Break the first item of the current range into a single element, and try to start a new range with the second item\n        results.push(buildRange(currentRange.start));\n        currentRange = buildRange(currentRange.end);\n        completeRangeWithItem(currentRange, currentItem);\n      } else {\n        // Persist the current range and start a new one with current item\n        finalizeCurrentRange(results, currentRange);\n        currentRange = buildRange(currentItem);\n      }\n    }\n  }\n\n  finalizeCurrentRange(results, currentRange);\n\n  return results;\n}\n\nmodule.exports = compactField;\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,UAAUA,CAACC,IAAI,EAAE;EACxB,OAAO;IACLC,KAAK,EAAED,IAAI;IACXE,KAAK,EAAE;EACT,CAAC;AACH;AAEA,SAASC,qBAAqBA,CAACC,KAAK,EAAEJ,IAAI,EAAE;EAC1CI,KAAK,CAACC,GAAG,GAAGL,IAAI;EAChBI,KAAK,CAACE,IAAI,GAAGN,IAAI,GAAGI,KAAK,CAACH,KAAK;EAC/BG,KAAK,CAACF,KAAK,GAAG,CAAC;AACjB;AAEA,SAASK,oBAAoBA,CAACC,OAAO,EAAEC,YAAY,EAAEC,gBAAgB,EAAE;EACrE,IAAID,YAAY,EAAE;IAChB;IACA,IAAIA,YAAY,CAACP,KAAK,KAAK,CAAC,EAAE;MAC5BM,OAAO,CAACG,IAAI,CAACZ,UAAU,CAACU,YAAY,CAACR,KAAK,CAAC,CAAC;MAC5CO,OAAO,CAACG,IAAI,CAACZ,UAAU,CAACU,YAAY,CAACJ,GAAG,CAAC,CAAC;IAC5C,CAAC,MAAM;MACLG,OAAO,CAACG,IAAI,CAACF,YAAY,CAAC;IAC5B;EACF;EACA,IAAIC,gBAAgB,EAAE;IACpBF,OAAO,CAACG,IAAI,CAACD,gBAAgB,CAAC;EAChC;AACF;AAEA,SAASE,YAAYA,CAACC,GAAG,EAAE;EACzB,IAAIL,OAAO,GAAG,EAAE;EAChB,IAAIC,YAAY,GAAGK,SAAS;EAE5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACnC,IAAIE,WAAW,GAAGJ,GAAG,CAACE,CAAC,CAAC;IACxB,IAAI,OAAOE,WAAW,KAAK,QAAQ,EAAE;MACnC;MACAV,oBAAoB,CAACC,OAAO,EAAEC,YAAY,EAAEV,UAAU,CAACkB,WAAW,CAAC,CAAC;MACpER,YAAY,GAAGK,SAAS;IAC1B,CAAC,MAAM,IAAI,CAACL,YAAY,EAAE;MACxB;MACAA,YAAY,GAAGV,UAAU,CAACkB,WAAW,CAAC;IACxC,CAAC,MAAM,IAAIR,YAAY,CAACP,KAAK,KAAK,CAAC,EAAE;MACnC;MACAC,qBAAqB,CAACM,YAAY,EAAEQ,WAAW,CAAC;IAClD,CAAC,MAAM;MACL,IAAIR,YAAY,CAACH,IAAI,KAAKW,WAAW,GAAGR,YAAY,CAACJ,GAAG,EAAE;QACxD;QACAI,YAAY,CAACP,KAAK,EAAE;QACpBO,YAAY,CAACJ,GAAG,GAAGY,WAAW;MAChC,CAAC,MAAM,IAAIR,YAAY,CAACP,KAAK,KAAK,CAAC,EAAE;QAAE;QACrC;QACAM,OAAO,CAACG,IAAI,CAACZ,UAAU,CAACU,YAAY,CAACR,KAAK,CAAC,CAAC;QAC5CQ,YAAY,GAAGV,UAAU,CAACU,YAAY,CAACJ,GAAG,CAAC;QAC3CF,qBAAqB,CAACM,YAAY,EAAEQ,WAAW,CAAC;MAClD,CAAC,MAAM;QACL;QACAV,oBAAoB,CAACC,OAAO,EAAEC,YAAY,CAAC;QAC3CA,YAAY,GAAGV,UAAU,CAACkB,WAAW,CAAC;MACxC;IACF;EACF;EAEAV,oBAAoB,CAACC,OAAO,EAAEC,YAAY,CAAC;EAE3C,OAAOD,OAAO;AAChB;AAEAU,MAAM,CAACC,OAAO,GAAGP,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}