{"ast": null, "code": "'use strict';\n\nvar CronExpression = require('./expression');\nfunction CronParser() {}\n\n/**\n * Parse crontab entry\n *\n * @private\n * @param {String} entry Crontab file entry/line\n */\nCronParser._parseEntry = function _parseEntry(entry) {\n  var atoms = entry.split(' ');\n  if (atoms.length === 6) {\n    return {\n      interval: CronExpression.parse(entry)\n    };\n  } else if (atoms.length > 6) {\n    return {\n      interval: CronExpression.parse(atoms.slice(0, 6).join(' ')),\n      command: atoms.slice(6, atoms.length)\n    };\n  } else {\n    throw new Error('Invalid entry: ' + entry);\n  }\n};\n\n/**\n * Wrapper for CronExpression.parser method\n *\n * @public\n * @param {String} expression Input expression\n * @param {Object} [options] Parsing options\n * @return {Object}\n */\nCronParser.parseExpression = function parseExpression(expression, options) {\n  return CronExpression.parse(expression, options);\n};\n\n/**\n * Wrapper for CronExpression.fieldsToExpression method\n *\n * @public\n * @param {Object} fields Input fields\n * @param {Object} [options] Parsing options\n * @return {Object}\n */\nCronParser.fieldsToExpression = function fieldsToExpression(fields, options) {\n  return CronExpression.fieldsToExpression(fields, options);\n};\n\n/**\n * Parse content string\n *\n * @public\n * @param {String} data Crontab content\n * @return {Object}\n */\nCronParser.parseString = function parseString(data) {\n  var blocks = data.split('\\n');\n  var response = {\n    variables: {},\n    expressions: [],\n    errors: {}\n  };\n  for (var i = 0, c = blocks.length; i < c; i++) {\n    var block = blocks[i];\n    var matches = null;\n    var entry = block.trim(); // Remove surrounding spaces\n\n    if (entry.length > 0) {\n      if (entry.match(/^#/)) {\n        // Comment\n        continue;\n      } else if (matches = entry.match(/^(.*)=(.*)$/)) {\n        // Variable\n        response.variables[matches[1]] = matches[2];\n      } else {\n        // Expression?\n        var result = null;\n        try {\n          result = CronParser._parseEntry('0 ' + entry);\n          response.expressions.push(result.interval);\n        } catch (err) {\n          response.errors[entry] = err;\n        }\n      }\n    }\n  }\n  return response;\n};\n\n/**\n * Parse crontab file\n *\n * @public\n * @param {String} filePath Path to file\n * @param {Function} callback\n */\nCronParser.parseFile = function parseFile(filePath, callback) {\n  require('fs').readFile(filePath, function (err, data) {\n    if (err) {\n      callback(err);\n      return;\n    }\n    return callback(null, CronParser.parseString(data.toString()));\n  });\n};\nmodule.exports = CronParser;", "map": {"version": 3, "names": ["CronExpression", "require", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_parseEntry", "entry", "atoms", "split", "length", "interval", "parse", "slice", "join", "command", "Error", "parseExpression", "expression", "options", "fieldsToExpression", "fields", "parseString", "data", "blocks", "response", "variables", "expressions", "errors", "i", "c", "block", "matches", "trim", "match", "result", "push", "err", "parseFile", "filePath", "callback", "readFile", "toString", "module", "exports"], "sources": ["C:/Users/<USER>/Documents/ekrani/node_modules/cron-parser/lib/parser.js"], "sourcesContent": ["'use strict';\n\nvar CronExpression = require('./expression');\n\nfunction CronParser() {}\n\n/**\n * Parse crontab entry\n *\n * @private\n * @param {String} entry Crontab file entry/line\n */\nCronParser._parseEntry = function _parseEntry (entry) {\n  var atoms = entry.split(' ');\n\n  if (atoms.length === 6) {\n    return {\n      interval: CronExpression.parse(entry)\n    };\n  } else if (atoms.length > 6) {\n    return {\n      interval: CronExpression.parse(\n        atoms.slice(0, 6).join(' ')\n      ),\n      command: atoms.slice(6, atoms.length)\n    };\n  } else {\n    throw new Error('Invalid entry: ' + entry);\n  }\n};\n\n/**\n * Wrapper for CronExpression.parser method\n *\n * @public\n * @param {String} expression Input expression\n * @param {Object} [options] Parsing options\n * @return {Object}\n */\nCronParser.parseExpression = function parseExpression (expression, options) {\n  return CronExpression.parse(expression, options);\n};\n\n/**\n * Wrapper for CronExpression.fieldsToExpression method\n *\n * @public\n * @param {Object} fields Input fields\n * @param {Object} [options] Parsing options\n * @return {Object}\n */\nCronParser.fieldsToExpression = function fieldsToExpression (fields, options) {\n  return CronExpression.fieldsToExpression(fields, options);\n};\n\n/**\n * Parse content string\n *\n * @public\n * @param {String} data Crontab content\n * @return {Object}\n */\nCronParser.parseString = function parseString (data) {\n  var blocks = data.split('\\n');\n\n  var response = {\n    variables: {},\n    expressions: [],\n    errors: {}\n  };\n\n  for (var i = 0, c = blocks.length; i < c; i++) {\n    var block = blocks[i];\n    var matches = null;\n    var entry = block.trim(); // Remove surrounding spaces\n\n    if (entry.length > 0) {\n      if (entry.match(/^#/)) { // Comment\n        continue;\n      } else if ((matches = entry.match(/^(.*)=(.*)$/))) { // Variable\n        response.variables[matches[1]] = matches[2];\n      } else { // Expression?\n        var result = null;\n\n        try {\n          result = CronParser._parseEntry('0 ' + entry);\n          response.expressions.push(result.interval);\n        } catch (err) {\n          response.errors[entry] = err;\n        }\n      }\n    }\n  }\n\n  return response;\n};\n\n/**\n * Parse crontab file\n *\n * @public\n * @param {String} filePath Path to file\n * @param {Function} callback\n */\nCronParser.parseFile = function parseFile (filePath, callback) {\n  require('fs').readFile(filePath, function(err, data) {\n    if (err) {\n      callback(err);\n      return;\n    }\n\n    return callback(null, CronParser.parseString(data.toString()));\n  });\n};\n\nmodule.exports = CronParser;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,cAAc,GAAGC,OAAO,CAAC,cAAc,CAAC;AAE5C,SAASC,UAAUA,CAAA,EAAG,CAAC;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACAA,UAAU,CAACC,WAAW,GAAG,SAASA,WAAWA,CAAEC,KAAK,EAAE;EACpD,IAAIC,KAAK,GAAGD,KAAK,CAACE,KAAK,CAAC,GAAG,CAAC;EAE5B,IAAID,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO;MACLC,QAAQ,EAAER,cAAc,CAACS,KAAK,CAACL,KAAK;IACtC,CAAC;EACH,CAAC,MAAM,IAAIC,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;IAC3B,OAAO;MACLC,QAAQ,EAAER,cAAc,CAACS,KAAK,CAC5BJ,KAAK,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAC5B,CAAC;MACDC,OAAO,EAAEP,KAAK,CAACK,KAAK,CAAC,CAAC,EAAEL,KAAK,CAACE,MAAM;IACtC,CAAC;EACH,CAAC,MAAM;IACL,MAAM,IAAIM,KAAK,CAAC,iBAAiB,GAAGT,KAAK,CAAC;EAC5C;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAF,UAAU,CAACY,eAAe,GAAG,SAASA,eAAeA,CAAEC,UAAU,EAAEC,OAAO,EAAE;EAC1E,OAAOhB,cAAc,CAACS,KAAK,CAACM,UAAU,EAAEC,OAAO,CAAC;AAClD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAd,UAAU,CAACe,kBAAkB,GAAG,SAASA,kBAAkBA,CAAEC,MAAM,EAAEF,OAAO,EAAE;EAC5E,OAAOhB,cAAc,CAACiB,kBAAkB,CAACC,MAAM,EAAEF,OAAO,CAAC;AAC3D,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACAd,UAAU,CAACiB,WAAW,GAAG,SAASA,WAAWA,CAAEC,IAAI,EAAE;EACnD,IAAIC,MAAM,GAAGD,IAAI,CAACd,KAAK,CAAC,IAAI,CAAC;EAE7B,IAAIgB,QAAQ,GAAG;IACbC,SAAS,EAAE,CAAC,CAAC;IACbC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,CAAC;EACX,CAAC;EAED,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGN,MAAM,CAACd,MAAM,EAAEmB,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;IAC7C,IAAIE,KAAK,GAAGP,MAAM,CAACK,CAAC,CAAC;IACrB,IAAIG,OAAO,GAAG,IAAI;IAClB,IAAIzB,KAAK,GAAGwB,KAAK,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;;IAE1B,IAAI1B,KAAK,CAACG,MAAM,GAAG,CAAC,EAAE;MACpB,IAAIH,KAAK,CAAC2B,KAAK,CAAC,IAAI,CAAC,EAAE;QAAE;QACvB;MACF,CAAC,MAAM,IAAKF,OAAO,GAAGzB,KAAK,CAAC2B,KAAK,CAAC,aAAa,CAAC,EAAG;QAAE;QACnDT,QAAQ,CAACC,SAAS,CAACM,OAAO,CAAC,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;MAC7C,CAAC,MAAM;QAAE;QACP,IAAIG,MAAM,GAAG,IAAI;QAEjB,IAAI;UACFA,MAAM,GAAG9B,UAAU,CAACC,WAAW,CAAC,IAAI,GAAGC,KAAK,CAAC;UAC7CkB,QAAQ,CAACE,WAAW,CAACS,IAAI,CAACD,MAAM,CAACxB,QAAQ,CAAC;QAC5C,CAAC,CAAC,OAAO0B,GAAG,EAAE;UACZZ,QAAQ,CAACG,MAAM,CAACrB,KAAK,CAAC,GAAG8B,GAAG;QAC9B;MACF;IACF;EACF;EAEA,OAAOZ,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACApB,UAAU,CAACiC,SAAS,GAAG,SAASA,SAASA,CAAEC,QAAQ,EAAEC,QAAQ,EAAE;EAC7DpC,OAAO,CAAC,IAAI,CAAC,CAACqC,QAAQ,CAACF,QAAQ,EAAE,UAASF,GAAG,EAAEd,IAAI,EAAE;IACnD,IAAIc,GAAG,EAAE;MACPG,QAAQ,CAACH,GAAG,CAAC;MACb;IACF;IAEA,OAAOG,QAAQ,CAAC,IAAI,EAAEnC,UAAU,CAACiB,WAAW,CAACC,IAAI,CAACmB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChE,CAAC,CAAC;AACJ,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAGvC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}