{"compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["components/*"], "@/pages/*": ["pages/*"], "@/utils/*": ["utils/*"], "@/data/*": ["data/*"], "@/assets/*": ["assets/*"]}, "target": "es5", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx"}, "include": ["src", "components", "pages", "utils", "data", "assets"], "exclude": ["node_modules", "build"]}