export function setupMidnightInterval(
  callback: () => void,
  intervalCallback: () => void
) {
  const now = new Date();
  const midnight = new Date(
    now.getFullYear(),
    now.getMonth(),
    now.getDate() + 1,
    0,
    10,
    0
  );
  const timeUntilMidnight = midnight.getTime() - now.getTime();

  // Set up the interval to execute the function at midnight
  const intervalId = setInterval(() => {
    // Call the callback function
    callback();

    // Recalculate the milliseconds until the next midnight
    const nextMidnight = new Date(midnight.getTime() + 24 * 60 * 60 * 1000);
    const nextTimeUntilMidnight = nextMidnight.getTime() - Date.now();

    // Update the interval delay to the next midnight
    clearInterval(intervalId);
    setTimeout(intervalCallback, nextTimeUntilMidnight);
  }, timeUntilMidnight);

  // Cleanup the interval on component unmount
  return () => {
    clearInterval(intervalId);
  };
}
